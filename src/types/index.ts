export interface TitleSubtitleProps {
    title: string;
    subtitle?: string;
    align?: "left" | "center" | "right";
    className?: string;
}

export interface Track {
    id: string;
    title: string;
    type: string;
    solved: number;
    available: number;
    vote: number;
}

export interface ProgressCircleProps {
    total: number;
    solved?: number;
    available?: number;
    type: "single" | "double" | "percent";
    reverseAvailable?: boolean;
    title?: string;
    size?: "sm" | "md" | "lg";
    className?: string;
    color?: string;
    secondaryColor?: string;
    "aria-label"?: string;
}

export interface TooltipProps {
    content: React.ReactNode;
    children: React.ReactElement;
    count?: number;
    delay?: number;
    className?: string;
}

export interface User {
    id: string;
    name: string;
    title: string;
    group: string;
    avatar: string;
    problems: number;
    submissions: number;
    dedicatedTime: string;
    rating: number;
    badges: string[];
    imageString?: string;
}

export interface UsersState {
    users: User[];
    filteredUsers: User[];
    loading: boolean;
    error: string | null;
    searchTerm: string;
    selectedGroup: string;
    viewMode: "card" | "list";
}

export interface SearchBarProps {
    value: string;
    onChange: (value: string) => void;
    placeholder?: string;
}

export interface DropdownProps {
    value: string;
    onChange: (value: string) => void;
    options: DropdownOption[];
    placeholder?: string;
}

export interface DropdownOption {
    value: string;
    label: string;
}

export interface ViewToggleProps {
    viewMode: "card" | "list";
    onViewChange: (mode: "card" | "list") => void;
}

export interface UserCardProps {
    user: User;
    // imageString?:string;
    // onCardClick: (userId:string )=> void;
}

export interface UserListProps {
    users: User[];
}

export interface TabNavigationProps {
    activeTab: "users" | "groups" | "countries";
    onTabChange: (tab: "users" | "groups" | "countries") => void;
}

export interface UsersContainerProps {
    users: User[];
    groupOptions: DropdownOption[];
    type?: "users" | "groups" | "countries";
}
