// start of daily problems types

export interface Problem {
  id: string;
  title: string;
  source: string;
  difficulty: string;
  tags: string[];
  solvedCount: number;
  upvotes: number;
  solveUrl: string;
  newSolutionUrl: string;
};

export interface DailyProblemsState {
  dailyProblems: Problem[];
  filteredProblems: Problem[];
  loading: boolean;
  error: string | null;
  searchTerm: string;
  selectedSuperGroup: string;
  selectedProblems: Problem[];
  allProblems?: Problem[];
}

export interface DailyProblemStatus {
  id: string;
  userId: string;
  status: string;
  timestamp: string;
  problemId: string;
}
