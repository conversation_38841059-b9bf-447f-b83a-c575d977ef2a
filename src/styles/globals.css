@import url("https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,100..900;1,100..900&display=swap");
@import "tailwindcss";

body {
    margin: 0px;
    color: rgb(33, 43, 54);
    line-height: 1.5;
    font-size: 1rem;
    font-family: "Public Sans", sans-serif;
    font-weight: 400;
    background-color: rgb(255, 255, 255);

    margin: 0;
    padding: 0;
    min-height: 100vh;
    background-color: inherit;
    color: inherit;
}

/* Hide scrollbar by default, show on hover */
.sidebar-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
}
.sidebar-scrollbar::-webkit-scrollbar {
    display: none;
}
.sidebar-scrollbar:hover {
    scrollbar-width: thin;
    -ms-overflow-style: auto;
}
.sidebar-scrollbar:hover::-webkit-scrollbar {
    display: block;
}

html {
    scroll-behavior: smooth;
    font-family: "Inter", sans-serif;
    background-color: #f9fafb;
    color: #111827;
}

main {
    padding: 1rem;
}

a {
    text-decoration: none;
}

* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    box-sizing: border-box;
}
