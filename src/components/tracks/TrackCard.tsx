import ProgressCircle from "@/components/ui/ProgressCircle";
import Tooltip from "@/components/ui/Tooltip";
import { Track } from "@/types";

type TrackCardProps = Track;

export default function TrackCard({
  id,
  title,
  type,
  solved,
  available,
  vote,
}: TrackCardProps) {
  const total = solved + available;

  return (
    <article
      className="rounded-3xl shadow-lg shadow-[#F7F8F9] border-gray-100 p-6 md:p-8 border bg-white flex flex-col gap-6"
      data-testid={`track-card-${id}`}
    >
      {/* Header */}
      <header className="flex justify-between items-start">
        <h3 className="font-bold text-xl text-gray-800">{title}</h3>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-1 text-gray-500">
            <Tooltip content="Upvote" count={vote}>
              <button
                className="p-1.5 hover:text-green-600 transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 rounded-full"
                aria-label={`Upvote ${title} track`}
              >
                <img 
                  src="/icons/upvote.svg"
                  width="24"
                  height="24"
                  alt="Upvote"
                  className="w-6 h-6"
                  aria-hidden="true"
                />
              </button>
            </Tooltip>
            <span className="text-sm font-medium">{vote}</span>
            <Tooltip content="Downvote" count={vote}>
              <button
                className="p-1.5 hover:text-red-600 transition-colors cursor-pointer"
                aria-label={`Downvote ${title} track`}
              >
                <img 
                  src="/icons/downvote.svg"
                  width="24"
                  height="24"
                  alt="Downvote"
                  className="w-6 h-6"
                  aria-hidden="true"
                />
              </button>
            </Tooltip>
          </div>
          <Tooltip content="Comments" count={0}>
            <button
              className="p-1.5 text-gray-500 hover:text-blue-600 transition-colors cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 rounded-full"
              aria-label={`Comments for ${title} track`}
            >
              <img 
                src="/icons/comments.svg"
                width="24"
                height="24"
                alt="Comments"
                className="w-6 h-6"
                aria-hidden="true"
              />
            </button>
          </Tooltip>
        </div>
      </header>

      {/* Progress Circle */}
      <div className="flex justify-center py-2">
        <ProgressCircle
          total={total}
          solved={solved}
          available={available}
          type="double"
          reverseAvailable={false}
          title={type}
          size="lg"
          className="transform scale-110"
        />
      </div>

      {/* Problem Counts */}
      <div className="flex flex-col gap-4 text-base font-medium">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span
              className="w-4 h-4 rounded bg-[#00AB55]"
              aria-hidden="true"
            ></span>
            <span className="text-gray-600">Solved</span>
          </div>
          <span className="text-gray-800 font-semibold">
            {solved} {solved === 1 ? "Problem" : "Problems"}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <span
              className="w-4 h-4 rounded bg-gray-100 border border-gray-200"
              aria-hidden="true"
            ></span>
            <span className="text-gray-600">Available</span>
          </div>
          <span className="text-gray-800 font-semibold">
            {available} {available === 1 ? "Problem" : "Problems"}
          </span>
        </div>
      </div>

      {/* Buttons */}
      <div className="flex flex-col gap-3 pt-2 mt-auto">
        <button
          className="bg-[#00AB55] hover:bg-[#007B55] text-white py-2.5 px-4 rounded-xl font-bold transition-all shadow-md shadow-[#CFEFDF] hover:shadow-lg hover:shadow-[#7DDEAD] text-base cursor-pointer focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
          aria-label={`Start exercise for ${title} track`}
          title={`Start exercise for ${title} track`}
        >
          Exercise
        </button>
        <button
          className="border border-[#00AB55] text-[#00AB55] py-2.5 px-4 rounded-xl font-bold hover:bg-green-50 transition-all text-base cursor-pointer focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
          aria-label={`View problems for ${title} track`}
          title={`View problems for ${title} track`}
        >
          Problems
        </button>
      </div>
    </article>
  );
}
