import React, { useState } from 'react';
import './AddTrack.scss';
import ProblemSelect from './ProblemSelect';

const superGroups = ['Remote', 'Onsite'];
const tracks = ['Progress', 'Python', 'Camp I', 'Progress II', 'AtCoder: Educational DP Tasks', 'Camp II'];
const problems = ['Problem 1', 'Problem 2', 'Problem 3'];

const AddTrack: React.FC = () => {
  // Tab state
  const [activeTab, setActiveTab] = useState<'create' | 'delete'>('create');

  // Form state (Create)
  const [trackName, setTrackName] = useState('');
  const [superGroup, setSuperGroup] = useState('');
  const [selectedProblems, setSelectedProblems] = useState<string[]>([]);
  const [reassignTrack, setReassignTrack] = useState('');
  const [touched, setTouched] = useState<{[key: string]: boolean}>({});

  // Form state (Delete)
  const [deleteTrack, setDeleteTrack] = useState('');

  // Validation
  const isTrackNameValid = trackName.trim() !== '';
  const isSuperGroupValid = superGroup.trim() !== '';
  const isFormValid = isTrackNameValid && isSuperGroupValid;
  const isReassignTrackValid = reassignTrack.trim() !== '' && selectedProblems.length > 0;
  const isDeleteTrackValid = deleteTrack.trim() !== '';

  // Handlers
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) return;
    // Submit logic here
    alert('Track submitted!');
  };

  const handleReassign = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isReassignTrackValid) return;
    // Reassign logic here
    alert(`Reassigned problems to ${reassignTrack}`);
  };

  const handleDelete = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isDeleteTrackValid) return;
    // Delete logic here
    alert(`Deleted track: ${deleteTrack}`);
  };

  const handleBlur = (field: string) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
  };

  return (
    <div className="add-track-container full-width">
      {/* Tab Navigation */}
      <div className="tab-nav">
        <div
          className={`tab-item${activeTab === 'create' ? ' active' : ''}`}
          onClick={() => setActiveTab('create')}
        >
          Create
        </div>
        <div
          className={`tab-item${activeTab === 'delete' ? ' active' : ''}`}
          onClick={() => setActiveTab('delete')}
        >
          Delete
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'create' ? (
        <>
          <form className="add-track-form" onSubmit={handleSubmit}>
            <h2>Add Track</h2>
            <div className="form-row">
              <div className="form-group">
                <input
                  type="text"
                  placeholder=" "
                  value={trackName}
                  onChange={e => setTrackName(e.target.value)}
                  onBlur={() => handleBlur('trackName')}
                  className={touched.trackName && !isTrackNameValid ? 'error' : ''}
                  id="trackName"
                  autoComplete="off"
                />
                <label htmlFor="trackName" className="floating-label">Track name</label>
                {touched.trackName && !isTrackNameValid && (
                  <span className="error-text">Track name is required</span>
                )}
              </div>
              <div className="form-group">
                <select
                  value={superGroup}
                  onChange={e => setSuperGroup(e.target.value)}
                  onBlur={() => handleBlur('superGroup')}
                  className={touched.superGroup && !isSuperGroupValid ? 'error' : ''}
                  id="superGroup"
                >
                  <option value="" disabled></option>
                  {superGroups.map(superGroup => (
                    <option key={superGroup} value={superGroup}>{superGroup}</option>
                  ))}
                </select>
                <label htmlFor="superGroup" className="floating-label">Select Super Group</label>
                {touched.superGroup && !isSuperGroupValid && (
                  <span className="error-text">Super group is required</span>
                )}
              </div>
            </div>
            <button type="submit" className="submit-btn" disabled={!isFormValid}>
              Submit
            </button>
          </form>

          <form className="reassign-problem-section" onSubmit={handleReassign}>
            <h2>Reassign Problem to Track</h2>
            <div className="form-row">
              <div className="form-group ">
                <ProblemSelect
                  options={problems}
                  value={selectedProblems}
                  onChange={setSelectedProblems}
                  label="Select problem(s)"
                />
              </div>
            </div>
            <div className="form-row">
              <div className="form-group full-width">
                <select
                  value={reassignTrack}
                  onChange={e => setReassignTrack(e.target.value)}
                  id="reassignTrack"
                >
                  <option value="" disabled></option>
                  {tracks.map(t => (
                    <option key={t} value={t}>{t}</option>
                  ))}
                </select>
                <label htmlFor="reassignTrack" className="floating-label">Select Track</label>
              </div>
            </div>
            <button type="submit" className="submit-btn" disabled={!isReassignTrackValid}>
              Submit
            </button>
          </form>
        </>
      ) : (
        <form className="delete-track-section" onSubmit={handleDelete}>
          <h2>Delete track</h2>
          <div className="form-row">
            <div className="form-group full-width">
              <select
                value={deleteTrack}
                onChange={e => setDeleteTrack(e.target.value)}
                id="deleteTrack"
              >
                <option value="" disabled>Select track</option>
                {tracks.map(t => (
                  <option key={t} value={t}>{t}</option>
                ))}
              </select>
            </div>
            <button
              type="submit"
              className="delete-btn"
              disabled={!isDeleteTrackValid}
              style={{ marginLeft: '24px' }}
            >
              Delete
            </button>
          </div>
        </form>
      )}
    </div>
  );
};

export default AddTrack; 