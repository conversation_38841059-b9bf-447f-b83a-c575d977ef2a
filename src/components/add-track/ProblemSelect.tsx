import React, { useState, useRef } from 'react';
import '../Contests/GroupSelect.scss'; // Reuse or override styles as needed

interface ProblemSelectProps {
  options: string[];
  value: string[];
  onChange: (value: string[]) => void;
  label?: string;
  placeholder?: string;
}

const ProblemSelect: React.FC<ProblemSelectProps> = ({ options, value, onChange, label = 'Select problem(s)', placeholder = '' }) => {
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState('');
  const selectRef = useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);

  const isFloating = open || value.length > 0 || search.length > 0;

  const filteredOptions = options.filter(opt =>
    opt.toLowerCase().includes(search.toLowerCase()) && !value.includes(opt)
  );

  const handleSelect = (opt: string) => {
    if (!value.includes(opt)) {
      onChange([...value, opt]);
    }
    setSearch('');
    setOpen(true);
  };

  const handleRemove = (opt: string) => {
    onChange(value.filter(v => v !== opt));
  };

  return (
    <div className={`group-select-root${open ? ' open' : ''}`}> 
      <div
        className={`group-select${open ? ' open' : ''}`}
        tabIndex={0}
        ref={selectRef}
        onClick={() => setOpen(true)}
      >
        <label className={`group-label${isFloating ? ' floating' : ''}${open ? ' active' : ''}`}>
          {label}
        </label>
        <div className="group-chips-input">
          {value.map(opt => (
            <span className="group-chip" key={opt}>
              {opt}
              <button className="chip-remove" onClick={e => { e.stopPropagation(); handleRemove(opt); }}>×</button>
            </span>
          ))}
          <input
            className="group-search-input"
            value={search}
            onChange={e => setSearch(e.target.value)}
            onFocus={() => setOpen(true)}
            placeholder={value.length === 0 ? placeholder : ''}
          />
        </div>
        <span className={`group-arrow${open ? ' open' : ''}`}>▼</span>
        {open && (
          <ul className="group-dropdown">
            {filteredOptions.map(opt => (
              <li
                key={opt}
                onClick={e => { e.stopPropagation(); handleSelect(opt); }}
                className={value.includes(opt) ? 'selected' : ''}
              >
                <span>{opt}</span>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default ProblemSelect; 