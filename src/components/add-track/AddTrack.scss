// =============================================================================
// ADD TRACK COMPONENT STYLES
// =============================================================================

.add-track-container {
  max-width: 950px;
  margin: 40px auto;
  background: #fff;
  border-radius: 10px;
  padding: 32px 32px 24px 32px;

  // Responsive adjustments
  @media (max-width: 700px) {
    padding: 0 8px;
  }

  // Tab Navigation
  // =============================================================================
  .tab-nav {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    gap: 0;
    margin-bottom: 48px;
    position: relative;
    height: 56px;

    // Responsive adjustments
    @media (max-width: 700px) {
      height: 48px;
      font-size: 18px;
    }

    .tab-item {
      flex: 1 1 0;
      text-align: center;
      font-size: 14px;
      font-weight: 600;
      color: #6B7684;
      cursor: pointer;
      padding-bottom: 12px;
      transition: color 0.2s;
      position: relative;

      // Active state
      &.active {
        color: #23272F;

        &::after {
          content: "";
          display: block;
          height: 3px;
          width: 80%;
          background: #16C172;
          border-radius: 2px;
          position: absolute;
          left: 10%;
          bottom: 0;
        }
      }
    }
  }

  // Form Sections
  // =============================================================================
  .add-track-form,
  .reassign-problem-section,
  .delete-track-section {
    background: none;
    border: none;
    margin-bottom: 32px;
    padding: 0 42px;

    h2{
      font-size: 1.2rem;
      font-weight: 700;
      color: #23272F;
      margin-bottom: 22px;
    }
  }

  // Delete Track Section
  // =============================================================================
  .delete-track-section {
    margin-top: 64px;

    .form-row {
      display: flex;
      flex-direction: row;
      align-items: center;
      gap: 5px;
    }

    .form-group {
      &.full-width {
        flex: 1 1 0;
      }
    }
  }

  // Form Layout
  // =============================================================================
  .form-row {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 20px;
    margin-top: 10px;
  }

  .form-group {
    flex: 1;
    position: relative;

    &.full-width {
      flex: 2;
    }

    // Form Inputs
    // =============================================================================
    input,
    select {
      width: 100%;
      padding: 18px 10px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 16px;
      // background: #f9fafb;
      margin-bottom: 4px;

      // Error state
      &.error {
        border-color: #e53e3e;
      }

      // Focus state
      &:focus {
        border-color: #16C172;
        outline: none;
      }
    }

    // Floating Labels
    // =============================================================================
    .floating-label {
      position: absolute;
      left: 12px;
      top: 16px;
      background: #fff;
      padding: 0 4px;
      color: #6b7280;
      font-size: 14px;
      pointer-events: none;
      transition: 0.2s;
    }

    // Floating label animations
    input:focus + .floating-label,
    input:not(:placeholder-shown) + .floating-label,
    select:focus + .floating-label,
    select:has(option:checked:not([value=""])) ~ .floating-label {
      top: -10px;
      left: 8px;
      font-size: 12px;
      color: #16C172;
      background: #fff;
    }

    // Error Messages
    // =============================================================================
    .error-text {
      color: #e53e3e;
      font-size: 12px;
      margin-top: -4px;
      margin-bottom: 4px;
      display: block;
    }
  }

  // Buttons
  // =============================================================================
  .submit-btn {
    width: 100%;
    padding: 12px;
    background: #16C172;
    color: #fff;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    margin-top: 8px;
    transition: background 0.2s;

    &:disabled {
      background: #d1d5db;
      color: #6b7280;
      cursor: not-allowed;
    }
  }

  .delete-btn {
    min-width: 100px;
    height: 56px;
    background: #E5E8EB;
    color: #A0AAB4;
    font-size: 1rem;
    font-weight: 600;
    border: none;
    border-radius: 8px;
    cursor: not-allowed;
    transition: background 0.2s, color 0.2s;

    &:enabled {
      background: #FF4D4F;
      color: #fff;
      cursor: pointer;
    }

    // Responsive adjustments
    @media (max-width: 700px) {
      max-width: 900px;
      height: 48px;
      font-size: 1.1rem;
    }
  }
} 