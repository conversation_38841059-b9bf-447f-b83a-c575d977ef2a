import React from 'react';
import Dropdown from '@/components/common/Dropdown';
import { SessionType } from '@/types';

interface SessionTypeSelectProps {
  value: SessionType | null;
  onChange: (value: SessionType) => void;
  error?: string;
}

const SessionTypeSelect: React.FC<SessionTypeSelectProps> = ({ value, onChange, error }) => {
  const options = ['Check in', 'Check out'].map(type => ({
    value: type,
    label: type
  }));

  return (
    <div >

    <Dropdown
      value={value || ''}
      onChange={(optionValue) => onChange(optionValue as SessionType)}
      options={options}
      placeholder="Type"
      error={error}
    />
    </div>
  );
};

export default SessionTypeSelect;