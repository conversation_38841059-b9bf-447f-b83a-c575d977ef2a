import React from "react";
import StatusButton from "@/components/take-attendance/StatusButton";
import StudentAvatar from "@/components/take-attendance/StudentAvatar";
import { AttendanceStatus, User } from "@/types/index";
import Link from "next/link";
import { useAttendance } from "@/hooks/useAttendance";

interface TakeAttendanceListProps {
  users: User[];
  onClick: (userId: string, status: AttendanceStatus) => void;
}

const TakeAttendanceList: React.FC<TakeAttendanceListProps> = ({
  users,
  onClick,
}) => {
  return (
    <div className="space-y-3 mb-6">
      {users.map((user) => (
        <div
          key={user.id}
          className={`flex items-center justify-between p-1 rounded-lg duration-150 ease-in-out ${
            !user.status
              ? "bg-white border border-gray-200"
              : user.status === "LATE"
              ? "bg-blue-100"
              : user.status === "ABSENT"
              ? "bg-red-100"
              : user.status === "EXCUSED"
              ? "bg-yellow-100"
              : user.status === "PRESENT"
              ? "bg-green-100"
              : "bg-white border border-gray-200"
          }`}
        >
          <div className="col-span-4 flex items-center space-x-3">
            <StudentAvatar src={user.avatar} name={user.name} />
            <div>
              <Link href="#">
                <div className="font-medium hover:cursor-pointer text-gray-800 hover:underline">
                  {user.name}
                </div>
              </Link>
            </div>
          </div>{" "}
          <div className="flex items-center space-x-2">
            {["LATE", "ABSENT", "EXCUSED", "PRESENT"].map((statusOption) => (
              <StatusButton
                key={statusOption}
                status={statusOption as AttendanceStatus}
                isActive={user.status === statusOption}
                onClick={(userId, status) => onClick(user.id, status)}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default TakeAttendanceList;
