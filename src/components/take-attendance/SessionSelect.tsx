import React from 'react';
import Dropdown from '@/components/common/Dropdown';
// import { Sessions } from '@/types';

interface SessionSelectProps {
  value: number | null;
  onChange: (value: number) => void;
  error?: string;
}

const SessionSelect: React.FC<SessionSelectProps> = ({ value, onChange, error }) => {
  const options = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(sessionNumber => ({
    value: sessionNumber,
    label: sessionNumber.toString()
  }));

  return (
    <div className="flex-1">

    <Dropdown
      value={value?.toString() || ''}
      onChange={(optionValue) => onChange(Number(optionValue))}
      options={options}
      placeholder="Session"
      error={error}
      />
    </div>
  );
};

export default SessionSelect;