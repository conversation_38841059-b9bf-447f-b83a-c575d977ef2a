import React, { useState } from 'react';
import Image from 'next/image';

interface StudentAvatarProps {
  src: string;
  name: string;
}

const StudentAvatar: React.FC<StudentAvatarProps> = ({ src, name }) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  return (
    <div className="rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold">
      <div className="w-10 h-10 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-300">
        {!imageError ? (
          <Image
            src={src}
            alt={name}
            fill
            className="w-full h-full object-cover"
            onError={handleImageError}
          />
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold">
            {name.charAt(0)}
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentAvatar;