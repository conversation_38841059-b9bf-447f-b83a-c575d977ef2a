import { AttendanceStatus } from "@/types/index";
import { statusConfig } from "@/utils/attendance-config";
// Status Button Component

interface StatusButtonProps {
  status: AttendanceStatus;
  isActive: boolean;
  onClick: (userId: string, status: AttendanceStatus) => void;
}

const StatusButton: React.FC<StatusButtonProps> = ({
  status,
  isActive,
  onClick,
}) => {
  const config = statusConfig[status];
  const Icon = config.icon;
  return (
    <button
      onClick={(e) => {
        e.preventDefault();
        console.log("Status button clicked:", status);
        onClick("user-id", status);
      }}
      className={`p-2 rounded-full  transition-all duration-200 ${
        isActive
          ? `${config.activeBg} border-transparent`
          : `${config.bgColor} border-gray-200 ${config.color}`
      }`}
      title={status}
    >
      <Icon className="w-4 h-4" />
    </button>
  );
};

export default StatusButton;
