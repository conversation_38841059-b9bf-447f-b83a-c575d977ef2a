import React, { useEffect } from "react";
import SessionSelect from "./SessionSelect";
import TitleSubtitle from "@/components/common/TitleSubtitle";
import SessionTypeSelect from "./SessionTypeSelect";
import TakeAttendanceList from "./TakeAttendanceList";
import { useAttendance } from "@/hooks/useAttendance";
import { MOCK_USERS, GROUP_OPTIONS } from "@/utils/constants";
import { useAppSelector } from "../../hooks/useAppSelector";
// import { User, SessionType,AttendanceStatus } from "@/types";
import { SessionType, AttendanceStatus, User } from "@/types";

const TakeAttendanceLayout: React.FC = () => {
  const {
    currentSession,
    sessionUsers,
    validationErrors,
    isSubmitting,
    error,
    isSessionConfigured,
    updateSessionNumber,
    updateSessionType,
    initializeSession,
    updateUserStatus,
    markAllPresent,
    handleSubmitAttendance,
  } = useAttendance();

  // Get users from your existing users slice
  // const { users } = useAppSelector((state) => state.users);
  const users = MOCK_USERS;

  // Initialize session when users are available
  useEffect(() => {
    if (
      users.length > 0 &&
      currentSession.sessionNumber &&
      currentSession.sessionType
    ) {
      // Assuming you want to filter users by group or use all users
      const sessionUsers = users; // or filter by group: users.filter(user => user.group === selectedGroup)
      initializeSession(
        currentSession.sessionNumber,
        currentSession.sessionType,
        "default-group", // or get from somewhere
        sessionUsers
      );
    }
  }, [
    users,
    currentSession.sessionNumber,
    currentSession.sessionType,
    initializeSession,
  ]);

  const handleSessionNumberChange = (sessionNumber: number) => {
    updateSessionNumber(sessionNumber);
  };

  const handleSessionTypeChange = (sessionType: SessionType) => {
    updateSessionType(sessionType);
  };
  const handleUserStatusChange = (userId: string, status: AttendanceStatus) => {
    console.log("Handling status change:", { userId, status });
    updateUserStatus(userId, status);
  };

  const handleEveryonePresent = () => {
    console.log("Setting everyone present...");
    markAllPresent();
    // Force a re-render by updating the component state
    const updatedUsers = users.map((user) => ({
      ...user,
      status: "PRESENT" as AttendanceStatus,
    }));
    console.log("Updated users:", updatedUsers);
  };
  const handleSubmit = async () => {
    console.log("Current Session State:", {
      sessionNumber: currentSession.sessionNumber,
      sessionType: currentSession.sessionType,
      group: currentSession.group,
      sessionUsers,
    });

    if (!currentSession.sessionNumber || !currentSession.sessionType) {
      console.log("Cannot submit: Session is not fully configured");
      return;
    }

    // Check if all users have a status set
    const usersWithNoStatus = Object.values(sessionUsers).filter(
      (userRecord) => userRecord.status === null
    );

    if (usersWithNoStatus.length > 0) {
      console.error("Cannot submit: Some users have no status set");
      const userNames = usersWithNoStatus
        .map((record) => record.user.name)
        .join(", ");
      console.error("Users with no status:", userNames);
      return;
    }

    console.log("Submitting attendance...");
    const success = await handleSubmitAttendance();

    if (success) {
      console.log("✅ Attendance submitted successfully!");
      console.log("Submitted Data:", {
        sessionNumber: currentSession.sessionNumber,
        sessionType: currentSession.sessionType,
        group: currentSession.group,
        attendanceRecords: Object.entries(sessionUsers).map(
          ([userId, data]) => ({
            userId,
            status: data.status,
            userName: data.user.name,
          })
        ),
      });
    } else {
      console.error("❌ Attendance submission failed");
      if (validationErrors.session || validationErrors.sessionType) {
        console.error("Validation errors:", validationErrors);
      }
      if (error) {
        console.error("Submission error:", error);
      }
    }
  };
  
  return (
    <div className="max-w-4xl mx-auto  ">
      <TitleSubtitle title={"Take attendance"} align={"left"} />
      {/* Dropdowns */}
      <div className="flex gap-4 mb-6">
        <SessionSelect
          value={currentSession.sessionNumber}
          onChange={(optionValue) =>
            handleSessionNumberChange(optionValue as number)
          }
          error={validationErrors.session || ""}
        />

        <SessionTypeSelect
          value={currentSession.sessionType}
          onChange={(optionValue) =>
            handleSessionTypeChange(optionValue as SessionType)
          }
          error={validationErrors.sessionType || ""}
        />
      </div>
      {/* Everyone Present Button */}
      <div className="mb-6">
        <button
          onClick={handleEveryonePresent}
          className="w-full py-3 text-green-700 font-medium rounded-lg  hover:border-green-100 hover:bg-green-100 transition-colors hover:cursor-pointer"
        >
          Everyone Present
        </button>
      </div>{" "}
      {/* Students List */}
      {isSessionConfigured && (
        <TakeAttendanceList
          users={users.map((user) => ({
            ...user,
            status: sessionUsers[user.id]?.status || "PRESENT",
          }))}
          onClick={handleUserStatusChange}
        />
      )}
      {/* Submit Button */}
      {isSessionConfigured && (
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="w-full py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700  hover:cursor-pointer transition-colors"
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </button>
      )}
      {/* Error Display */}
      {error && <div className="error-message">{error}</div>}
    </div>
  );
};

export default TakeAttendanceLayout;
