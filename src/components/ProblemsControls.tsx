import { Download, Filter } from 'lucide-react';
import { ColumnsDropdown } from './ColumnsDropdown';
import { ExportDropdown } from './ExportDropdown';
import { FiltersDropdown } from './FiltersDropdown';

interface ProblemsControlsProps {
  visibleColumns: Record<string, boolean>;
  toggleColumn: (key: string) => void;
  showExportDropdown: boolean;
  setShowExportDropdown: (show: boolean) => void;
  showFiltersDropdown: boolean;
  setShowFiltersDropdown: (show: boolean) => void;
  showColumnsDropdown: boolean;
  setShowColumnsDropdown: (show: boolean) => void;
  exportOptions: Array<{ label: string; action: () => void }>;
  filters: any[];
  addFilter: () => void;
  removeFilter: (id: number) => void;
  newFilter: any;
  setNewFilter: (filter: any) => void;
 
}

export const ProblemsControls = ({
  visibleColumns,
  toggleColumn,
  showExportDropdown,
  setShowExportDropdown,
  showFiltersDropdown,
  setShowFiltersDropdown,
  showColumnsDropdown,
  setShowColumnsDropdown,
  exportOptions,
  filters,
  addFilter,
  removeFilter,
  newFilter,
  setNewFilter,
}: ProblemsControlsProps) => {
  const handleColumnsClick = () => {
    setShowColumnsDropdown(!showColumnsDropdown);
    setShowFiltersDropdown(false);
    setShowExportDropdown(false);
  };

  const handleFiltersClick = () => {
    setShowFiltersDropdown(!showFiltersDropdown);
    setShowColumnsDropdown(false);
    setShowExportDropdown(false);
  };

  const handleExportClick = () => {
    setShowExportDropdown(!showExportDropdown);
    setShowColumnsDropdown(false);
    setShowFiltersDropdown(false);
  };

  return (
    <div className="flex items-center gap-2 p-4 bg-gray-50 border-b border-gray-200">
      {/* Columns Dropdown */}
      <div className="relative">
        <button
          onClick={handleColumnsClick}
          className="flex items-center gap-2 px-3 py-2 rounded hover:bg-gray-100 transition-colors text-sm text-gray-700 font-semibold"
        >
          <div className="grid grid-cols-3 gap-0.5 w-3 h-3">
            {[...Array(9)].map((_, i) => (
              <div key={i} className="w-1 h-1 bg-gray-500 rounded-full"></div>
            ))}
          </div>
          Columns
        </button>
        
        {showColumnsDropdown && (
          <ColumnsDropdown
            visibleColumns={visibleColumns}
            toggleColumn={toggleColumn}
          />
        )}
      </div>

      {/* Filters Dropdown */}
      <div className="relative">
        <button
          onClick={handleFiltersClick}
          className="flex items-center gap-2 px-3 py-2 rounded hover:bg-gray-100 transition-colors text-sm text-gray-700 font-semibold"
        >
          <Filter className="w-4 h-4" />
          Filters
        </button>
        
        {showFiltersDropdown && (
          <FiltersDropdown
            filters={filters}
            addFilter={addFilter}
            removeFilter={removeFilter}
            newFilter={newFilter}
            setNewFilter={setNewFilter}
          />
        )}
      </div>

      {/* Export Dropdown */}
      <div className="relative">
        <button
          onClick={handleExportClick}
          className="flex items-center gap-2 px-3 py-2 rounded hover:bg-gray-100 transition-colors text-sm text-gray-700 font-semibold"
        >
          <Download className="w-4 h-4" />
          Export
        </button>
        
        {showExportDropdown && (
          <ExportDropdown options={exportOptions} />
        )}
      </div>
    </div>
  );
};