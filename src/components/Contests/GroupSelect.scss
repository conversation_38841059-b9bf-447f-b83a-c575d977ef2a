.group-select-root {
  width: 100%;

  .group-select {
    position: relative;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: #fff;
    min-height: 54px;
    padding-top: 0;
    padding-bottom: 0;
    cursor: pointer;
    transition: border-color 0.2s;
    display: flex;
    align-items: center;
    font-size: 1rem;
    width: 100%;

    &.open {
      border-color: #22c55e;
    }

    .group-label {
      position: absolute;
      left: 1.5rem;
      top: .8rem;
      color: #94a3b8;
      font-size: 1rem;
      pointer-events: none;
      transition: all 0.2s;
      background: transparent;

      &.floating {
        top: -0.7rem;
        left: 1rem;
        font-size: 0.8rem;
        color: #22c55e;
        background: #fff;
        padding: 0 0.2rem;
      }
      &.active {
        color: #22c55e;
      }
    }

    .group-chips-input {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.5rem;
      min-height: 2.2rem;
      flex: 1;
      padding: 0.24rem 1rem;
    }

    .group-chip {
      display: flex;
      align-items: center;
      background: #f3f4f6;
      border-radius: 2rem;
      padding: 0.4rem 0.9rem 0.4rem 0.9rem;
      font-size: 1rem;
      color: #22292f;
      margin-bottom: 0.2rem;
      gap: 0.5rem;
    }

    .chip-remove {
      background: none;
      border: none;
      color: #64748b;
      font-size: 1.1rem;
      margin-left: 0.3rem;
      cursor: pointer;
      padding: 0;
      line-height: 1;
    }

    .group-search-input {
      border: none;
      outline: none;
      font-size: 1rem;
      flex: 1;
      min-width: 120px;
      background: transparent;
      margin-left: 0.2rem;
      margin-bottom: 0.2rem;
    }

    .clear-all {
      background: none;
      border: none;
      color: #64748b;
      font-size: 1.3rem;
      cursor: pointer;
      position: absolute;
      right: 1.5rem;
      top: .9rem;
      z-index: 2;
      padding: 0;
      line-height: 1;
      margin-right: 1rem;
    }

    .group-arrow {
      position: absolute;
      right: 1rem;
      top: 1rem;
      color: #64748b;
      font-size: 0.8rem;
      pointer-events: none;
      transition: transform 0.2s;
      &.open {
        transform: rotate(180deg);
      }
    }

    .group-dropdown {
      position: absolute;
      left: 0;
      right: 0;
      top: calc(100% + 0.2rem);
      background: #fff;
      border-radius: 8px;
      z-index: 10;
      max-height: 350px;
      overflow-y: auto;
      box-shadow: 0 2px 16px rgba(0,0,0,0.08);
      padding: 0.5rem 0;

      li {
        display: flex;
        align-items: center;
        gap: 1.1rem;
        padding: 1rem 2rem;
        font-size: 1rem;
        cursor: pointer;
        transition: background 0.15s;
        &.selected {
          background: #e6f9ed;
          input[type="checkbox"] {
            accent-color: #22c55e;
          }
        }
      }
    }
  }
}

.group-select.open {
  border-color: #22c55e;
}

.group-label {
  position: absolute;
  left: 1.5rem;
  top: .8rem;
  color: #94a3b8;
  font-size: 1rem;
  pointer-events: none;
  transition: all 0.2s;
  background: transparent;
}

.group-label.floating {
  top: -0.7rem;
  left: 1rem;
  font-size: 0.8rem;
  color: #22c55e;
  background: #fff;
  padding: 0 0.2rem;
}

.group-label.active {
  color: #22c55e;
}

.group-value {
  flex: 1;
  color: #64748b;
  font-size: 1rem;
  margin-left: 0.2rem;
}

.group-arrow {
  position: absolute;
  right: 1rem;
  top: 1rem;
  color: #64748b;
  font-size: 0.8rem;
  pointer-events: none;
  transition: transform 0.2s;
}

.group-arrow.open {
  transform: rotate(180deg);
}

.group-dropdown {
  position: absolute;
  left: 0;
  right: 0;
  top: calc(100% + 0.2rem);
  background: #fff;
  border-radius: 8px;
  z-index: 10;
  max-height: 350px;
  overflow-y: auto;
  box-shadow: 0 2px 16px rgba(0,0,0,0.08);
  padding: 0.5rem 0;
}

.group-dropdown li {
  display: flex;
  align-items: center;
  gap: 1.1rem;
  padding: 1rem 2rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background 0.15s;
}

.group-dropdown li:hover {
  background: #f0fdf4;
}

.group-dropdown input[type="checkbox"] {
  width: 1.3rem;
  height: 1.3rem;
  accent-color: #22c55e;
}

.group-dropdown .selected {
  background: #f0fdf4;
  color: #22c55e;
}

.group-chips-input {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
  min-height: 2.2rem;
  flex: 1;
  padding: 0.2rem 0;
}

.group-chip {
  display: flex;
  align-items: center;
  background: #f3f4f6;
  border-radius: 2rem;
  padding: 0.2rem 0.9rem 0.2rem 0.9rem;
  font-size: 1rem;
  color: #22292f;
  margin-bottom: 0.2rem;
  gap: 0.5rem;
}

.chip-remove {
  background: none;
  border: none;
  color: #64748b;
  font-size: 1.1rem;
  margin-left: 0.3rem;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.group-search-input {
  border: none;
  outline: none;
  font-size: 1rem;
  flex: 1;
  min-width: 120px;
  background: transparent;
  margin-left: 0.2rem;
  margin-bottom: 0.2rem;
}

.group-dropdown li.selected {
  background: #e6f9ed;
}

.group-dropdown li.selected input[type="checkbox"] {
  accent-color: #22c55e;
} 