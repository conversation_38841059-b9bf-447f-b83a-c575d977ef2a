.layout {
  display: flex;
  flex-direction: column;
  // gap: 2rem;
  width: 100%;
  max-width: 1400px;
  margin:  auto;

  .header {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
  }
  
  .subheader {
    color: #6b7280;
    font-size: 1.1rem;
    margin-bottom: 2rem;
  }

  .filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    .input {
      flex:1;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-size: 1rem;
    }
  }

  .mainWrapper {
    display: flex;
    gap: 2rem;
    .main {
      flex: 2;
    }
    .sidebarr {
      flex: 1;
    }
    
  }
  
}


// Responsive styles
@media (max-width: 900px) {
  .layout {
    padding: 0 1rem;
  }
  .mainWrapper {
    flex-direction: column;
    gap: 1.5rem;
  }
  .sidebarr {
    width: 100%;
    margin-top: 1.5rem;
    min-width: 0;
    flex: 1;
  }
  .main {
    width: 100%;
    min-width: 0;
  }
  .filters {
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 0;
  }
}

@media (max-width: 600px) {
 
  .filters {
    gap: 0.5rem;
  }
  .group-select-root,
  .country-select-root {
    width: 100%;
    max-width: 100%;
  }
  .group-select,
  .country-select {
    width: 100%;
    min-width: 0;
    font-size: 0.95rem;
  }
  .mainWrapper {
    gap: 1rem;
  }
  .main, .sidebarr {
    width: 100%;
    min-width: 0;
    flex: 1;
  }
} 
