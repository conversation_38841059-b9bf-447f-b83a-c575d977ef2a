'use client'
import React, { useState, useRef } from 'react';
import './CountrySelect.scss';

const countries = [
  { name: 'Algeria', code: 'dz' },
  { name: 'Angola', code: 'ao' },
  { name: 'Benin', code: 'bj' },
  { name: 'Cameroon', code: 'cm' },
  { name: 'Chad', code: 'td' },
  { name: 'China', code: 'cn' },
  { name: 'Egypt', code: 'eg' },
  { name: 'Ethiopia', code: 'et' },
  { name: 'Ghana', code: 'gh' },
  { name: 'Kenya', code: 'ke' },
  { name: 'Nigeria', code: 'ng' },
  { name: 'Tunisia', code: 'tn' },
  { name: 'Tanzania', code: 'tz' },
  { name: 'Uganda', code: 'ug' },
  { name: 'Zambia', code: 'zm' },
  { name: 'Zimbabwe', code: 'zw' },
  { name: 'Morocco', code: 'ma' },
  { name: 'Mozambique', code: 'mz' },
  { name: 'Namibia', code: 'na' },
  { name: 'Nepal', code: 'np' },
  { name: 'Niger', code: 'ne' },
  { name: 'Rwanda', code: 'rw' }
];

const CountrySelect: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<string[]>([]);
  const [search, setSearch] = useState('');
  const selectRef = useRef<HTMLDivElement>(null);

  // Close dropdown if clicked outside
  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);

  const handleSelect = (country: string) => {
    setSelected(prev =>
      prev.includes(country) ? prev.filter(c => c !== country) : [...prev, country]
    );
  };

  const handleRemove = (country: string) => {
    setSelected(prev => prev.filter(c => c !== country));
  };

  const handleClearAll = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelected([]);
  };

  const isFloating = open || selected.length > 0 || search.length > 0;

  const filteredCountries = countries.filter(c =>
    c.name.toLowerCase().includes(search.toLowerCase()) ||
    c.code.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className={`country-select-root${open ? ' open' : ''}`}> 
      <div
        className={`country-select${open ? ' open' : ''}`}
        tabIndex={0}
        ref={selectRef}
        onClick={() => setOpen(true)}
      >
        <label className={`country-label${isFloating ? ' floating' : ''}${open ? ' active' : ''}`}>
          Countries
        </label>
        <div className="country-chips-input">
          {selected.map(code => {
            const country = countries.find(country => country.code === code);
            return country ? (
              <span className="country-chip" key={code}>
                {country.name} - {country.code}
                <button className="chip-remove" onClick={e => { e.stopPropagation(); handleRemove(code); }}>×</button>
              </span>
            ) : null;
          })}
          <input
            className="country-search-input"
            value={search}
            onChange={e => setSearch(e.target.value)}
            onFocus={() => setOpen(true)}
            placeholder={selected.length === 0 ? '' : undefined}
          />
        </div>
        {selected.length > 0 && (
          <button className="clear-all" onClick={handleClearAll} tabIndex={-1}>×</button>
        )}
        <span className={`country-arrow${open ? ' open' : ''}`}>▼</span>
        {open && (
          <ul className="country-dropdown">
            {filteredCountries.map(country => (
              <li
                key={country.code}
                onClick={e => { e.stopPropagation(); handleSelect(country.code); }}
                className={selected.includes(country.code) ? 'selected' : ''}
              >
                <input
                  type="checkbox"
                  checked={selected.includes(country.code)}
                  readOnly
                />
                <span>{country.name} - {country.code}</span>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default CountrySelect; 