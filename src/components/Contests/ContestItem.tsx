import React from 'react';
import './ContestItem.scss';

interface ContestItemProps {
  title: string;
  problems: number;
  timeAgo: string;
}

const ContestItem: React.FC<ContestItemProps> = ({ title, problems, timeAgo }) => (
  <div className="card">
    <div>
      <div className="title">{title}</div>
      <div className="meta">{problems} problems · {timeAgo}</div>
    </div>
    <div className="icons">
      <div className="icon share-icon" role="img" aria-label="Share" />
      <div className="icon external-link-icon" role="img" aria-label="External Link" />
    </div>
  </div>
);

export default ContestItem; 