import React from 'react';
import './ContestsPageLayout.scss';
import CountrySelect from './CountrySelect';
import GroupSelect from './GroupSelect';
import TitleSubtitle from '../common/TitleSubtitle';

interface ContestsPageLayoutProps {
  children: React.ReactNode;
  sidebar: React.ReactNode;
}

const ContestsPageLayout: React.FC<ContestsPageLayoutProps> = ({ children, sidebar }) => (
  <div className="layout">
    <TitleSubtitle title="Contests" subtitle="Ratings and Contests" />
    <div className="filters">
      <GroupSelect />
      <CountrySelect />
    </div>
    <div className="mainWrapper">
      <div className="main">{children}</div>
      <div className="sidebarr">{sidebar}</div>
    </div>
  </div>
);

export default ContestsPageLayout; 