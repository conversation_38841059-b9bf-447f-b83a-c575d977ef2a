import React from 'react';
import './ContestsList.scss';
import ContestItem from './ContestItem';

const contests = [
  { id: 153, title: 'A2SV Ghana G6 - Round #12', problems: 7, timeAgo: '4d ago' },
  { id: 152, title: 'A2SV G6 - Round #13', problems: 5, timeAgo: '17d ago' },
  { id: 151, title: 'A2SV G6 - Round #12', problems: 5, timeAgo: '17d ago' },
  { id: 150, title: 'A2SV G6 - Round #12', problems: 5, timeAgo: '17d ago' },
  { id: 149, title: 'A2SV G6 - Round #12', problems: 5, timeAgo: '17d ago' },
  { id: 148, title: 'A2SV G6 - Round #12', problems: 5, timeAgo: '17d ago' },
  { id: 147, title: 'A2SV G6 - Round #12', problems: 5, timeAgo: '17d ago' },
  { id: 146, title: 'A2SV G6 - Round #12', problems: 5, timeAgo: '17d ago' },
  { id: 145, title: 'A2SV G6 - Round #12', problems: 5, timeAgo: '17d ago' },
  { id: 144, title: 'A2SV G6 - Round #12', problems: 5, timeAgo: '17d ago' },
];

const ContestsList: React.FC = () => (
  <div className="listWrapper">
    <div className="contests">
      {contests.map(contest => (
        <ContestItem key={contest.id} {...contest} />
      ))}
    </div>
  </div>
);

export default ContestsList; 