.sidebar {
  background: #fff;
  border-radius: 18px;
  padding: 1.5rem 1rem;
  display: flex;
  flex-direction: column;
  box-shadow:  rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px;
  gap: .7rem;
  .ratings-header-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .header {
      font-size: 1.1rem;
      font-weight: 700;
    }
    .visual-label {
      display: flex;
      align-items: center;
      gap: 0.3rem;
      color: #22c55e;
      font-weight: 600;
      font-size: 1.1rem;
      .visual-icon svg {
        color: #22c55e;
        vertical-align: middle;
      }
      
      .visual-text {
        color: #22c55e;
        font-size: 1rem;
      } 
    }
  }
  .subheader {
    color: #6b7280;
    font-size: .9rem;
    font-weight: 500;
    margin-bottom: 1rem;
  }
  .users {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
}

@media (max-width: 900px) {
  .sidebar {
    width: 100%;
    min-width: 0;
    padding: 1rem 0.5rem;
  }
}
@media (max-width: 600px) {
  .sidebar {
    width: 100%;
    min-width: 0;
    padding: 0.7rem 0.2rem;
  }
  .users {
    gap: 0.7rem;
  }
  .name {
    font-size: 0.85rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 120px;
    display: inline-block;
  }
}

.visual-icon {
  .icon {
    width: 22px;
    height: 22px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    background-color: currentColor;

    &.ratings-network-icon {
      mask-image: url('/ratings-network-icon.svg');
      -webkit-mask-image: url('/ratings-network-icon.svg');
    }
  }
}






