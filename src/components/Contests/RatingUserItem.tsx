import React from 'react';
import './RatingUserItem.scss';

interface RatingUserItemProps {
  name: string;
  group: string;
  rating: number;
  rank?: number;
  badgeIcon?: React.ReactNode;
  avatarUrl?: string;
}

const defaultAvatar = 'https://ui-avatars.com/api/?name=User&background=22c55e&color=fff&size=44';

const RatingUserItem: React.FC<RatingUserItemProps> = ({ name, group, rating, rank, badgeIcon, avatarUrl }) => (
  <div className="userRow">
    <div className="avatar-rank-wrapper">
      <img
        className="user-avatar"
        src={avatarUrl || defaultAvatar}
        alt="User avatar"
        width={44}
        height={44}
      />
      {typeof rank === 'number' && (
        <span className="rank-badge">{rank || 2}</span>
      )}
    </div>
    <div className="info">
      <div className="name">{name}</div>
      <div className="group-badge-row">
        <span className="group-icon">
          {badgeIcon || <div className="icon group-badge-icon" role="img" aria-label="Group Badge" />}
        </span>
        <span className="group">{group}</span>
      </div>
    </div>
    <div className="rating">{rating}</div>
  </div>
);

export default RatingUserItem; 