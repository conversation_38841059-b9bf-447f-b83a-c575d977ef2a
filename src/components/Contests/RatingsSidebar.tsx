import React from 'react';
import './RatingsSidebar.scss';
import RatingUserItem from './RatingUserItem';

const users = [
  { id: 1, name: '<PERSON><PERSON>', group: 'G5A Student', rating: 2683 },
  { id: 2, name: '<PERSON><PERSON><PERSON>', group: 'G5C Student', rating: 2405 },
  { id: 3, name: '<PERSON>', group: 'G5C Student', rating: 2284 },
  { id: 4, name: '<PERSON><PERSON><PERSON>', group: 'G47 Student', rating: 2256 },
  { id: 5, name: '<PERSON><PERSON><PERSON>', group: 'G5A Student', rating: 2228 },
  { id: 6, name: '<PERSON><PERSON>', group: 'G6H Student', rating: 2209 },
  { id: 7, name: '<PERSON><PERSON>', group: 'G6H Student', rating: 2209 },
  { id: 8, name: '<PERSON><PERSON>', group: 'G6H Student', rating: 2209 },
  { id: 9, name: '<PERSON><PERSON>', group: 'G6H Student', rating: 2209 },
  { id: 10, name: '<PERSON><PERSON>', group: 'G6H Student', rating: 2209 },
  { id: 11, name: '<PERSON><PERSON>', group: 'G6H Student', rating: 2209 },
  { id: 12, name: 'Raouf Ould Ali', group: 'G6H Student', rating: 2209 },
  { id: 13, name: 'Raouf Ould Ali', group: 'G6H Student', rating: 2209 },
];

const RatingsSidebar: React.FC = () => (
  <div className="sidebar">
    <div className="ratings-header-row">
      <span className="header">Ratings</span>
      <span className="visual-label">
        <span className="visual-icon">
          <div className="icon ratings-network-icon" role="img" aria-label="Network" />
        </span>
        <span className="visual-text">Visual</span>
      </span>
    </div>
    <div className="subheader">There are 1349 users with ratings</div>
    <div className="users">
      {users.map(user => (
        <RatingUserItem key={user.id} {...user} />
      ))}
    </div>
  </div>
);

export default RatingsSidebar; 