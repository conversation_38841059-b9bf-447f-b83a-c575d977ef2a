.userRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;

  .avatar-rank-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    margin-right: 0.7rem;

    .user-avatar {
      width: 44px;
      height: 44px;
      border-radius: 50%;
      object-fit: cover;
      flex-shrink: 0;
    }

    .rank-badge {
      position: absolute;
      left: -8px;
      bottom: -6px;
      background: #fff;
      border: 2px solid #e5e7eb;
      color: #222;
      font-size: 1rem;
      font-weight: 500;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 1px 2px rgba(0,0,0,0.06);
    }
  }
  .info {
    display: flex;
    flex-direction: column;
    flex: 1;
    .name {
      font-weight: 700;
      font-size: 0.9rem;
      color: #22292f;
    }
    .group-badge-row {
      display: flex;
      align-items: center;
      gap: 0.4rem;
      margin-top: 0.1rem;
      .group-icon {
        .icon {
          width: 24px;
          height: 24px;
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          mask-size: contain;
          mask-repeat: no-repeat;
          mask-position: center;
          -webkit-mask-size: contain;
          -webkit-mask-repeat: no-repeat;
          -webkit-mask-position: center;
          background-color: currentColor;

          &.group-badge-icon {
            mask-image: url('/group-badge-icon.svg');
            -webkit-mask-image: url('/group-badge-icon.svg');
            color: #22c55e;
          }
        }
      }
      
      .group {
        color: #6b7280;
        font-size: .8rem;
      }
    }
    .rating {
      font-weight: 500;
      color: #22292f;
      font-size: .9rem;
      margin-left: 1.2rem;
    } 
  }
}













