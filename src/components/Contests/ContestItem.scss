.card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  // border: 1px solid #e5e7eb;
  border-radius: 20px;
  padding: 1.8rem 1.5rem;
  gap: 1rem;
  box-shadow:  rgba(145, 158, 171, 0.2) 0px 0px 2px 0px, rgba(145, 158, 171, 0.12) 0px 12px 24px -4px;
  transition: box-shadow 300ms cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  
  .title {
    font-weight: 600;
    font-size: 1.1rem;
  }
  
  .meta {
    color: #6b7280;
    font-size: 0.95rem;
  }

  &:hover {
    background: #f2f7fb;
  }
}

.icons {
  display: flex;
  gap: 20px;
  color: #22c55e;


  .icon {
    width: 18px;
    height: 18px;
    color: #22c55e;
    cursor: pointer;
    transition: color 0.2s;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    background-color: currentColor;

    &.share-icon {
      mask-image: url('/share-icon.svg');
      -webkit-mask-image: url('/share-icon.svg');
    }

    &.external-link-icon {
      mask-image: url('/external-link-icon.svg');
      -webkit-mask-image: url('/external-link-icon.svg');
    }

    &:hover {
      color: #0a7a33;
    }
  }
}

@media (max-width: 600px) {
  .card {
    flex-direction: column;
    align-items: flex-start;
    padding: 1rem 0.7rem;
    gap: 0.7rem;
    width: 100%;

    .title {
      font-size: 1rem;
    }

    .meta {
      font-size: 0.85rem;
    }

    .icons {
      gap: 0.7rem;
    }
  }
}
