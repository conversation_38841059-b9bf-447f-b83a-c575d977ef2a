.country-select-root {
  width: 100%;
  .open {
    border-color: #22c55e;
  }
  .country-select {
    position: relative;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    background: #fff;
    min-height: 54px;
    padding-top: 0;
    padding-bottom: 0;
    padding-right: 2.5rem;
    cursor: pointer;
    transition: border-color 0.2s;
    display: flex;
    align-items: center;
    width: 100%;
    .country-label {
      position: absolute;
      left: 1.5rem;
      top: .8rem;
      color: #94a3b8;
      font-size: 1rem;
      pointer-events: none;
      transition: all 0.2s;
      background: transparent;
    }
    .floating {
      top: -0.7rem;
      left: 1rem;
      font-size: 0.8rem;
      color: #22c55e;
      background: #fff;
      padding: 0 0.2rem;
    }
    .active {
      color: #76c793;
    }
    .country-chips-input {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 0.5rem;
      min-height: 2.2rem;
      flex: 1;
      padding: 0.24rem 1rem;
      .country-chip {
        display: flex;
        align-items: center;
        background: #f3f4f6;
        border-radius: 2rem;
        padding: 0.4rem 0.9rem 0.4rem 0.9rem;
        font-size: 1rem;
        color: #22292f;
        margin-bottom: 0.2rem;
        gap: 0.5rem;
        .chip-remove {
          background: none;
          border: none;
          color: #64748b;
          font-size: 1.1rem;
          margin-left: 0.3rem;
          cursor: pointer;
          padding: 0;
          line-height: 1;
          
        }
      }
      .country-search-input {
        border: none;
        outline: none;
        font-size: 1rem;
        flex: 1;
        min-width: 120px;
        background: transparent;
        margin-left: 0.2rem;
        margin-bottom: 0.2rem;
      }
    }
    .clear-all {
      background: none;
      border: none;
      color: #64748b;
      font-size: 1.3rem;
      cursor: pointer;
      position: absolute;
      right: 1.5rem;
      top: .9rem;
      z-index: 2;
      padding: 0;
      line-height: 1;
      margin-right: 1rem;
    }
    .open {
      transform: rotate(180deg);
    }
    .country-arrow {
      position: absolute;
      right: 1rem;
      top: 1rem;
      color: #64748b;
      font-size: 0.8rem;
      pointer-events: none;
      transition: transform 0.2s;
    }
    .country-dropdown {
      position: absolute;
      left: 0;
      right: 0;
      top: calc(100% + 0.2rem);
      background: #fff;
      border-radius: 8px;
      z-index: 10;
      max-height: 350px;
      overflow-y: auto;
      box-shadow: 0 2px 16px rgba(0,0,0,0.08);
      padding: 0.5rem 0;
      .selected {
        background: #f0fdf4;
        color: #22c55e;
        input[type="checkbox"] {
          accent-color: #22c55e;
        } 
      }
      li {
        display: flex;
        align-items: center;
        gap: 1.1rem;
        padding: 1rem 2rem;
        font-size: 1rem;
        cursor: pointer;
        transition: background 0.15s;
        &:hover {
          background: #f0fdf4;
        }
        input {
          width: 1.3rem;
          height: 1.3rem;
          accent-color: #22c55e;
        }
      }
    }
  }
}
