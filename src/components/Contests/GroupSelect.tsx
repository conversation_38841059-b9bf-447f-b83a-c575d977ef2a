'use client'
import React, { useState, useRef } from 'react';
import './GroupSelect.scss';

const groups = [
  { name: 'A2SV AAIT Group 12', code: 'G12' },
  { name: 'A2SV AAIT Group 31', code: 'G31' },
  { name: 'A2SV AAIT Group 32', code: 'G32' },
  { name: 'A2SV AAIT Group 42', code: 'G42' },
  { name: 'A2SV AAIT Group 43', code: 'G43' },
  { name: 'A2SV AAIT Group 44', code: 'G44' },
  { name: 'A2SV AASTU Group 33', code: 'G33' },
  { name: 'A2SV AASTU Group 34', code: 'G34' },
  { name: 'A2SV AASTU Group 35', code: 'G35' },
  { name: 'A2SV AASTU Group 36', code: 'G36' },
  { name: 'A2SV AASTU Group 37', code: 'G37' },
  { name: 'A2SV AASTU Group 38', code: 'G38' },
  { name: 'A2SV AASTU Group 39', code: 'G39' },
  { name: 'A2SV AASTU Group 40', code: 'G40' },

];

const GroupSelect: React.FC = () => {
  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState<string[]>([]);
  const [search, setSearch] = useState('');
  const selectRef = useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }
    if (open) document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [open]);

  const handleSelect = (group: string) => {
    setSelected(prev =>
      prev.includes(group) ? prev.filter(g => g !== group) : [...prev, group]
    );
  };

  const handleRemove = (group: string) => {
    setSelected(prev => prev.filter(g => g !== group));
  };

  const handleClearAll = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelected([]);
  };

  const isFloating = open || selected.length > 0 || search.length > 0;

  const filteredGroups = groups.filter(g =>
    g.name.toLowerCase().includes(search.toLowerCase()) ||
    g.code.toLowerCase().includes(search.toLowerCase())
  );

  return (
    <div className={`group-select-root${open ? ' open' : ''}`}> 
      <div
        className={`group-select${open ? ' open' : ''}`}
        tabIndex={0}
        ref={selectRef}
        onClick={() => setOpen(true)}
      >
        <label className={`group-label${isFloating ? ' floating' : ''}${open ? ' active' : ''}`}>
          Groups
        </label>
        <div className="group-chips-input">
          {selected.map(code => {
            const group = groups.find(group => group.code === code);
            return group ? (
              <span className="group-chip" key={code}>
                {group.name} - {group.code}
                <button className="chip-remove" onClick={e => { e.stopPropagation(); handleRemove(code); }}>×</button>
              </span>
            ) : null;
          })}
          <input
            className="group-search-input"
            value={search}
            onChange={e => setSearch(e.target.value)}
            onFocus={() => setOpen(true)}
            placeholder={selected.length === 0 ? '' : undefined}
          />
        </div>
        {selected.length > 0 && (
          <button className="clear-all" onClick={handleClearAll} tabIndex={-1}>×</button>
        )}
        <span className={`group-arrow${open ? ' open' : ''}`}>▼</span>
        {open && (
          <ul className="group-dropdown">
            {filteredGroups.map(group => (
              <li
                key={group.code}
                onClick={e => { e.stopPropagation(); handleSelect(group.code); }}
                className={selected.includes(group.code) ? 'selected' : ''}
              >
                <input
                  type="checkbox"
                  checked={selected.includes(group.code)}
                  readOnly
                />
                <span>{group.name} - {group.code}</span>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default GroupSelect; 