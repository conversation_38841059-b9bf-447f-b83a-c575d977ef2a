// src/components/common/SearchBar.tsx
import React from "react";
import { SearchBarProps } from "@/types/index";
import Image from "next/image";
import closeicon from "../../../public/icons/closeicon.svg";
// import searchglass from "../../../public/icons/searchglass.svg";
const DailyProblemsInput: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "Select Problems...",
}) => {
  const handleClear = () => {
    onChange("");
  };
  return (
    <div className="">
     
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500"
        placeholder={placeholder}
      />
      {value && ( 
        <button
          type="button"
          onClick={handleClear}
          className="absolute inset-y-0 right-0 pr-3 flex items-center "
          aria-label="Clear search"
        >
          <div className="rounded-full focus:cursor-pointer hover:bg-gray-200 transition-colors p-1">
            <Image
              src={closeicon}
              alt="Clear search"
              className="w-5 h-5 text-gray-100 transition-colors "
              width={20}
            ></Image>
          </div>
        </button>
      )}
    </div>
  );
};

export default DailyProblemsInput;
