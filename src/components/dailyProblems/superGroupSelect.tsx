
import React from 'react';
import Dropdown from '@/components/common/Dropdown';
// import { SuperGroupType } from '@/types/dailyProblemsTypes';

interface SuperGroupTypeSelectProps {
  value: string | null;
  onChange: (value: string) => void;
  error?: string;
}

const SuperGroupTypeSelect: React.FC<SuperGroupTypeSelectProps> = ({ value, onChange, error }) => {
  const options = ['Remote', 'InPerson','Ghana inperson', 'G4 + G5 InPerson' ,'Generation 6 InPerson Education' ,'Generation 6 Remote Education','Generation 6 Ghana InPerson'].map(type => ({
    value: type,
    label: type
  }));



  return (
    <div >

    <Dropdown
      value={value || ''}
      onChange={(optionValue) => onChange(optionValue as string)}
      options={options}
      placeholder="Select Super Group"
      error={error}
    />
    </div>
  );
};

export default SuperGroupTypeSelect;