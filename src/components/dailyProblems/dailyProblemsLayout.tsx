import React, { useEffect, useState } from "react";

import TitleSubtitle from "@/components/common/TitleSubtitle";
import SuperGroupTypeSelect from "./superGroupSelect";
import { DailyProblemsCard } from "./dailyProblemsCard";
import DailyProblemsSelect from "./dailyProblemsSelect";
import { useDailyProblems } from "@/hooks/useDailyProblems";

// import { useAppSelector } from "../../hooks/useAppSelector";

import { Problem } from "@/types/dailyProblemsTypes";

const DailyProblemsLayout: React.FC = () => {
  const [problemsDisabled, setProblemsDisabled] = useState(true);
  // Sample problems data - replace with your actual data source
  const allProblems: Problem[] = [
    {
      id: "1",
      title: "Bubble Sort",
      source: "HackerRank",
      difficulty: "Easy",
      tags: ["Sorting", "Algorithms"],
      solvedCount: 12345,
      upvotes: 6789,
      solveUrl: "https://www.hackerrank.com/challenges/bubble-sort",
      newSolutionUrl:
        "https://www.hackerrank.com/challenges/bubble-sort/submissions",
    },
    {
      id: "2",
      title: "Bubble Sort Optimization",
      source: "HackerRank",
      difficulty: "Medium",
      tags: ["Sorting", "Algorithms", "Optimization"],
      solvedCount: 10000,
      upvotes: 5000,
      solveUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-optimization",
      newSolutionUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-optimization/submissions",
    },
    {
      id: "3",
      title: "Bubble Sort with Edge Cases",
      source: "HackerRank",
      difficulty: "Hard",
      tags: ["Sorting", "Algorithms", "Edge Cases"],
      solvedCount: 5000,
      upvotes: 3000,
      solveUrl: "https://www.hackerrank.com/challenges/bubble-sort-edge-cases",
      newSolutionUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-edge-cases/submissions",
    },
    {
      id: "4",
      title: "Bubble Sort Implementation",
      source: "HackerRank",
      difficulty: "Easy",
      tags: ["Sorting", "Algorithms", "Implementation"],
      solvedCount: 15000,
      upvotes: 7000,
      solveUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-implementation",
      newSolutionUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-implementation/submissions",
    },
    {
      id: "5",
      title: "Bubble Sort Variations",
      source: "HackerRank",
      difficulty: "Medium",
      tags: ["Sorting", "Algorithms", "Variations"],
      solvedCount: 11000,
      upvotes: 5500,
      solveUrl: "https://www.hackerrank.com/challenges/bubble-sort-variations",
      newSolutionUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-variations/submissions",
    },
    {
      id: "6",
      title: "Bubble Sort Performance",
      source: "HackerRank",
      difficulty: "Hard",
      tags: ["Sorting", "Algorithms", "Performance"],
      solvedCount: 4000,
      upvotes: 2500,
      solveUrl: "https://www.hackerrank.com/challenges/bubble-sort-performance",
      newSolutionUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-performance/submissions",
    },
    {
      id: "7",
      title: "Bubble Sort Debugging",
      source: "HackerRank",
      difficulty: "Easy",
      tags: ["Sorting", "Algorithms", "Debugging"],
      solvedCount: 13000,
      upvotes: 6500,
      solveUrl: "https://www.hackerrank.com/challenges/bubble-sort-debugging",
      newSolutionUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-debugging/submissions",
    },
    {
      id: "8",
      title: "Bubble Sort Best Practices",
      source: "HackerRank",
      difficulty: "Medium",
      tags: ["Sorting", "Algorithms", "Best Practices"],
      solvedCount: 12000,
      upvotes: 6000,
      solveUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-best-practices",
      newSolutionUrl:
        "https://www.hackerrank.com/challenges/bubble-sort-best-practices/submissions",
    },
  ];

  const {
    error,
    isActive,
    selectedSuperGroup,
    handleSuperGroupChange,
    handleProblemsChange,
    handleAddProblemsSubmit,
    handleSaveOrderSubmit,
    selectedProblems,
  } = useDailyProblems(undefined, allProblems);

  // Disable problems selection until a super group is selected
  useEffect(() => {
    setProblemsDisabled(!selectedSuperGroup);
  }, [selectedSuperGroup]);

  const sampleProblem: Problem = {
    id: "1",
    title: "Bubble Sort",
    source: "HackerRank",
    difficulty: "Easy",
    tags: ["Sorting", "Algorithms"],
    solvedCount: 12345,
    upvotes: 6789,
    solveUrl: "https://www.hackerrank.com/challenges/bubble-sort",
    newSolutionUrl:
      "https://www.hackerrank.com/challenges/bubble-sort/submissions",
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <TitleSubtitle title={"Daily Problems"} align={"left"} />

      {/* Super Group Dropdown */}
      <div className="mb-6">
        <SuperGroupTypeSelect
          value={selectedSuperGroup}
          onChange={handleSuperGroupChange}
          // error={error || ""}
        />
      </div>

      {/* Daily Problems Card */}
      {selectedSuperGroup && (
        <div className="">
          <DailyProblemsCard problem={sampleProblem} />
        </div>
      )}

      {/* Problems Selection Section */}
      <div className="space-y-4">
        <p className="text-sm text-gray-600">
          Please select some problems to add...
        </p>
        <div className="flex gap-3">
          <div className="flex-1 relative">
            {problemsDisabled ? (
              <div className="absolute inset-0 bg-white bg-opacity-60 cursor-not-allowed " />
            ) : (
              <DailyProblemsSelect
                value={selectedProblems}
                onChange={handleProblemsChange}
                error={error || ""}
                placeholder="Select problem(s)"
                allProblems={allProblems}
              />
            )}
          </div>
          <div className="w-24 h-12">
            <button
              onClick={handleAddProblemsSubmit}
              className={`px-4 py-2  font-medium rounded-lg transition-colors
              ${
                isActive && !problemsDisabled
                  ? "bg-green-600 text-white hover:bg-green-700"
                  : "bg-gray-200 text-gray-600 cursor-not-allowed"
              }
            `}
              disabled={!isActive || problemsDisabled}
            >
              Add Problems
            </button>
          </div>
        </div>

        {/* Save Order Button - Full Width */}
        <button
          onClick={handleSaveOrderSubmit}
          className="w-full py-3 bg-green-600 text-white font-bold rounded-lg hover:bg-green-700 transition-colors"
        >
          Save Order
        </button>
      </div>
    </div>
  );
};

export default DailyProblemsLayout;
