import Image from "next/image";
import Link from "next/link";

import chovrionDownButton from "../../../public/chovrionDownButton.svg";
import plusicon from "../../../public/plusicon.svg";
import chovrionupButton from "../../../public/chovrionupButton.svg";
import externalLinkIcon from "../../../public/external-link-icon.svg";
import clockIcon from "../../../public/clockIcon.svg";
import { Problem } from "@/types/dailyProblemsTypes";

type DailyProblemCardProps = {
  problem: Problem;
};

export const DailyProblemsCard: React.FC<DailyProblemCardProps> = ({
  problem,
}) => {
  const {
    title,
    source,
    difficulty,
    tags,
    solvedCount,
    upvotes,
    solveUrl,
    newSolutionUrl,
  } = problem;

  return (
    <div className="w-full mx-auto bg-[#cbefd9] p-6 rounded-xl shadow-sm text-gray-800 font-sans relative">
      {/* Top Header Section */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="text-sm font-bold text-gray-600">Daily problem</h3>
          <Image src={clockIcon} alt="Clock icon" width={16} height={16} />
        </div>

        {/* Upvote section in top-right */}
        <div className="flex items-center gap-1">
          <button
            aria-label="Upvote problem"
            className="transition-transform duration-200 hover:cursor-pointer hover:scale-110"
          >
            <Image src={chovrionupButton} alt="Upvote" width={16} height={16} />
          </button>
          <span className="font-medium text-gray-700">{upvotes}</span>
          <button
            aria-label="Downvote problem"
            className="transition-transform duration-200 hover:cursor-pointer hover:scale-110"
          >
            <Image
              src={chovrionDownButton}
              alt="Downvote"
              width={16}
              height={16}
            />
          </button>
        </div>
      </div>

      <p className="text-xs text-gray-500 mt-1">
        Refreshes every 24 hours and needs to be solved today!
      </p>

      {/* Main Content Area */}
      <div className="mt-4 flex justify-between items-start">
        {/* Left Side: Problem Details */}
        <div className="flex-1">
          <Link href={solveUrl}>
            <h2 className="text-xl font-bold hover:underline text-gray-800">
              {title}
            </h2>
          </Link>
          <p className="mt-1 text-xs font-black text-gray-700 capitalize">
            {source} • {difficulty} • {tags.join(", ")}
          </p>
        </div>

        {/* Right Side: Solved Count */}
        <div className="text-right ml-4">
          <p className="text-4xl font-bold text-gray-800">{solvedCount}</p>
          <p className="text-xs text-gray-500 -mt-1">Solved it</p>
        </div>
      </div>

      {/* Action Buttons Section */}
      <div className="mt-6 flex flex-col gap-2 max-w-xs">
        <Link
          href={solveUrl}
          className="flex items-center justify-center gap-2 px-4 py-2 bg-transparent border border-green-600 text-green-700 font-medium text-sm rounded-lg hover:bg-green-50 transition-colors duration-200"
        >
          <Image
            src={externalLinkIcon}
            alt="External link"
            width={14}
            height={14}
          />
          Solve It Now
        </Link>

        <button className="flex items-center justify-center gap-2 px-4 py-2 text-gray-600 font-medium text-sm rounded-lg hover:bg-green-300 transition-colors duration-200">
          <Link href={newSolutionUrl} className="flex items-center gap-2">
            <Image
              src={plusicon}
              alt="Plus icon"
              className="text-gray-800"
              width={14}
              height={14}
            />
            New Solution
          </Link>
        </button>
      </div>
    </div>
  );
};
