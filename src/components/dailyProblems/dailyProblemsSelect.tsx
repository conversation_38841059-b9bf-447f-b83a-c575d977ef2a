//dailyProblemsSelect.tsx

import React, { useState, useEffect } from "react";
import Image from "next/image";
import dropdownarrow from "../../../public/icons/dropdownarrow.svg";
import DailyProblemsInput from "@/components/dailyProblems/dailyProblemsInput";
import { Problem } from "@/types/dailyProblemsTypes";
import { useDailyProblems } from "@/hooks/useDailyProblems";

interface DailyProblemsSelectProps {
  value: Problem[] | null;
  onChange: (value: Problem[]) => void;
  error?: string;
  placeholder?: string;
  allProblems?: Problem[];
}

const DailyProblemsSelect: React.FC<DailyProblemsSelectProps> = ({
  value = [],
  onChange,
  placeholder = "Select problem(s)",
  allProblems = [],
}) => {
  const {
    filteredProblems,
    // error,
    searchTerm,
    handleSelect,
    handleRemoveTag,
    handleSearchChange,
    isSelected,
    selectedProblems,
  } = useDailyProblems(value, allProblems);

  const [isOpen, setIsOpen] = useState(false);

  // Notify parent when selection changes
  useEffect(() => {
    onChange(selectedProblems);
  }, [selectedProblems, onChange]);

  // Only show unselected problems in dropdown
  const availableProblems = filteredProblems.filter(
    (problem) => !selectedProblems.some((p) => p.id === problem.id)
  );

  const getDisplayText = () => {
    if (!value || value.length === 0) return placeholder;
    return `${value.length} problem(s) selected`;
  };

  return (
    <div className="relative">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className={`relative w-full bg-white border rounded-lg shadow-sm pl-3 pr-10 py-2 text-left cursor-pointer hover:ring-1 ring-black ring-opacity-5 focus:outline-none min-w-[120px] ${
          value && value.length > 0
            ? "border-green-500 focus:ring-green-500 focus:border-green-500"
            : "border-gray-300 focus:ring-green-500 focus:border-green-500"
        }`}
      >
        <span
          className={`block truncate ${
            value && value.length > 0 ? "text-green-600" : "text-gray-500"
          }`}
        >
          {getDisplayText()}
        </span>
        <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <Image
            src={dropdownarrow}
            alt="dropdown icon"
            className="w-5 h-5 text-gray-100 transition-colors"
            width={20}
          />
        </span>
      </button>

      {/* Selected items as tags */}
      {value && value.length > 0 && (
        <div className="mb-2 flex flex-wrap gap-2">
          {value.map((problem) => (
            <div
              key={problem.id}
              className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200"
            >
              <span className="truncate max-w-xs">{problem.title}</span>
              <button
                type="button"
                onClick={() => handleRemoveTag(problem.id)}
                className="ml-2 inline-flex items-center justify-center w-4 h-4 rounded-full text-blue-600 hover:bg-blue-200 hover:text-blue-800 focus:outline-none"
              >
                ×
              </button>
            </div>
          ))}
        </div>
      )}

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white shadow-lg max-h-80 rounded-md border border-gray-200 overflow-hidden focus:outline-none">
          {/* Search input */}
          <div className="p-3 border-b border-gray-200">
            <DailyProblemsInput
              value={searchTerm}
              onChange={handleSearchChange}
              placeholder="Enter space-separated email addresses"
            />
          </div>
          {/* Problems list */}
          <div className="max-h-60 overflow-y-auto">
            {availableProblems.length > 0 ? (
              availableProblems.map((problem) => (
                <button
                  key={problem.id}
                  onClick={() => handleSelect(problem)}
                  className={`w-full text-left px-4 py-3 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0 ${
                    isSelected(problem.id)
                      ? "bg-green-50 text-green-700"
                      : "text-gray-900"
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className="truncate text-sm">{problem.title}</span>
                    {isSelected(problem.id) && (
                      <span className="text-green-600 ml-2 flex-shrink-0">
                        ✓
                      </span>
                    )}
                  </div>
                </button>
              ))
            ) : (
              <div className="px-4 py-6 text-center text-gray-500 text-sm">
                {searchTerm
                  ? "No problems found matching your search."
                  : "No problems available."}
              </div>
            )}
          </div>
        </div>
      )}
      {/* {error && <p className="mt-1 text-sm text-red-600">{error}</p>} */}
    </div>
  );
};

export default DailyProblemsSelect;
