import React, { useState } from 'react';
import './AddProblem.scss';

const platforms = ['LeetCode', 'Codeforces', 'HackerRank'];
const difficulties = ['Easy', 'Medium', 'Hard'];
const tracks = ['Track 1', 'Track 2', 'Track 3'];
const contests = ['Contest 1', 'Contest 2', 'Contest 3'];
const problems = ['Problem A', 'Problem B', 'Problem C'];

const AddProblem: React.FC = () => {
  // Form state
  const [problemName, setProblemName] = useState('');
  const [tag, setTag] = useState('');
  const [platform, setPlatform] = useState('');
  const [difficulty, setDifficulty] = useState('');
  const [track, setTrack] = useState('');
  const [contest, setContest] = useState('');
  const [link, setLink] = useState('');
  const [deleteProblem, setDeleteProblem] = useState('');
  const [touched, setTouched] = useState<{[key: string]: boolean}>({});

  // Validation
  const isProblemNameValid = problemName.trim() !== '';
  const isTagValid = tag.trim() !== '';
  const isDifficultyValid = difficulty.trim() !== '';
  const isFormValid = isProblemNameValid && isTagValid && isDifficultyValid;

  // Handlers
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!isFormValid) return;
    // Submit logic here
    alert('Problem submitted!');
  };

  const handleDelete = () => {
    if (!deleteProblem) return;
    // Delete logic here
    alert(`Deleted ${deleteProblem}`);
  };

  const handleBlur = (field: string) => {
    setTouched((prev) => ({ ...prev, [field]: true }));
  };

  return (
    <div className="add-problem-container">
      <form className="add-problem-form" onSubmit={handleSubmit}>
        <h2>Add Problem</h2>
        <div className="form-row">
          <div className="form-group">
            <input
              type="text"
              placeholder=" "
              value={problemName}
              onChange={e => setProblemName(e.target.value)}
              onBlur={() => handleBlur('problemName')}
              className={touched.problemName && !isProblemNameValid ? 'error' : ''}
              id="problemName"
              autoComplete="off"
            />
            <label htmlFor="problemName" className="floating-label">Problem name</label>
            {touched.problemName && !isProblemNameValid && (
              <span className="error-text">Name of problem required</span>
            )}
          </div>
          <div className="form-group">
            <input
              type="text"
              placeholder=" "
              value={tag}
              onChange={e => setTag(e.target.value)}
              onBlur={() => handleBlur('tag')}
              className={touched.tag && !isTagValid ? 'error' : ''}
              id="tag"
              autoComplete="off"
            />
            <label htmlFor="tag" className="floating-label">Tag</label>
            {touched.tag && !isTagValid && (
              <span className="error-text">Tag is required</span>
            )}
          </div>
        </div>
        <div className="form-row">
          <div className="form-group">
            <select
              value={platform}
              onChange={e => setPlatform(e.target.value)}
              id="platform"
            >
              <option value="" disabled selected></option>
              {platforms.map(p => (
                <option key={p} value={p}>{p}</option>
              ))}
            </select>
            <label htmlFor="platform" className="floating-label">Select Platform</label>
          </div>
          <div className="form-group">
            <select
              value={difficulty}
              onChange={e => setDifficulty(e.target.value)}
              onBlur={() => handleBlur('difficulty')}
              className={touched.difficulty && !isDifficultyValid ? 'error' : ''}
              id="difficulty"
            >
              <option value="" disabled selected></option>
              {difficulties.map(d => (
                <option key={d} value={d}>{d}</option>
              ))}
            </select>
            <label htmlFor="difficulty" className="floating-label">Select Difficulty</label>
            {touched.difficulty && !isDifficultyValid && (
              <span className="error-text">Difficulty is required</span>
            )}
          </div>
        </div>
        <div className="form-row">
          <div className="form-group">
            <select
              value={track}
              onChange={e => setTrack(e.target.value)}
              id="track"
            >
              <option value="" disabled selected></option>
              {tracks.map(t => (
                <option key={t} value={t}>{t}</option>
              ))}
            </select>
            <label htmlFor="track" className="floating-label">Select Track</label>
          </div>
          <div className="form-group">
            <select
              value={contest}
              onChange={e => setContest(e.target.value)}
              id="contest"
            >
              <option value="" disabled selected></option>
              {contests.map(c => (
                <option key={c} value={c}>{c}</option>
              ))}
            </select>
            <label htmlFor="contest" className="floating-label">Select Contest</label>
          </div>
        </div>
        <div className="form-row">
          <div className="form-group full-width">
            <input
              type="text"
              placeholder=" "
              value={link}
              onChange={e => setLink(e.target.value)}
              id="link"
              autoComplete="off"
            />
            <label htmlFor="link" className="floating-label">Link</label>
          </div>
        </div>
        <button type="submit" className="submit-btn" >
          Submit
        </button>
      </form>

      <div className="delete-problem-section">
        <h3>Delete problem</h3>
        <div className="delete-row">
          <select
            value={deleteProblem}
            onChange={e => setDeleteProblem(e.target.value)}
          >
            <option value="">Select problem</option>
            {problems.map(p => (
              <option key={p} value={p}>{p}</option>
            ))}
          </select>
          <button
            className="delete-btn"
            disabled={!deleteProblem}
            onClick={handleDelete}
            type="button"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddProblem;
