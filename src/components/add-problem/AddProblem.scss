.add-problem-container {
  max-width: 850px;
  margin: 2rem auto;
  padding: 0 1rem;

  .add-problem-form {
    border-radius: 12px;
    padding: 2rem 2rem 1.5rem 2rem;
    // box-shadow: 0 2px 8px rgba(0,0,0,0.03);
    display: flex;
    flex-direction: column;
    gap: 1.5rem;

    h2 {
      font-size: 1.35rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      color: #111827;
    }

    .form-row {
      display: flex;
      gap: 1.5rem;
      width: 100%;
      
      & + .form-row {
        margin-top: 0.2rem;
      }

      .form-group {
        flex: 1;
        display: flex;
        flex-direction: column;
        position: relative;

        &.full-width {
          flex: 2;
        }

        input, select {
          padding: 1.3rem 1rem 0.6rem 1rem;
          border: 1.5px solid #d1d5db;
          border-radius: 8px;
          font-size: 1rem;
          outline: none;
          transition: border 0.2s;
          margin-bottom: 0.2rem;
          box-shadow: none;
          color: #111827;
        }

        select:invalid,
        select option[value=""] {
          color: #9ca3af;
        }

        input:focus, select:focus {
          border: 2px solid #22c55e;
          border-radius: 8px;
          box-shadow: none;
        }

        input.error, select.error {
          border-color: #ef4444;
        }

        .floating-label {
          position: absolute;
          left: 1rem;
          top: 1.15rem;
          font-size: 1rem;
          color: #b0b3b8;
          pointer-events: none;
          background: transparent;
          transition: all 0.18s cubic-bezier(0.4,0,0.2,1);
        }
      
        input:not(:placeholder-shown) ~ .floating-label,  
        select:has(option:checked:not([value=""])) ~ .floating-label {
          top: -0.65rem;
          left: 0.85rem;
          font-size: 0.82rem;
          background: #fafbfc;
          padding: 0 0.2rem;
        }

        select:focus ~ .floating-label,
        select:active ~ .floating-label,
        input:focus ~ .floating-label {
            background: #fafbfc;
            top: -0.65rem;
            left: 0.85rem;
            color: #22c55e;
        }

        .error-text {
          color: #ef4444;
          font-size: 0.85rem;
          margin-top: -0.2rem;
          margin-bottom: 0.1rem;
        }
      }
    }

    .submit-btn {
      width: 100%;
      padding: 0.9rem 0;
      background: #22c55e;
      color: #fff;
      border: none;
      border-radius: 8px;
      font-size: 1.1rem;
      font-weight: 600;
      cursor: pointer;
      margin-top: 0.5rem;
      transition: background 0.2s, color 0.2s;

      &:hover {
        outline: none;
        box-shadow: none;
        background: #3abb69;
      }

    }
  }

  .delete-problem-section {
    max-width: 770px;
    margin: 3.9rem auto;
    background: none;
    h3 {
      font-size: 1.1rem;
      font-weight: 700;
      color: #111827;
      margin-bottom: 1rem;
    }
    .delete-row {
      display: flex;
      gap: 1rem;
      align-items: center;
      select {
        flex: 1;
        padding: 0.8rem 1rem;
        border: 1.5px solid #d1d5db;
        border-radius: 8px;
        font-size: 1rem;
        background: #fafbfc;
        outline: none;
        transition: border 0.2s;
      }
      .delete-btn {
        padding: 0.8rem 1.5rem;
        background: #e5e7eb;
        color: #6b7280;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: not-allowed;
        transition: background 0.2s, color 0.2s;
        &:not(:disabled) {
          background: #ef4444;
          color: #fff;
          cursor: pointer;
        }
      }
    }
  }

  // Responsive styles
  @media (max-width: 600px) {
    max-width: 100%;
    .add-problem-form {
      padding: 1.2rem 0.5rem 1rem 0.5rem;
      .form-row {
        flex-direction: column;
        gap: 0.7rem;
      }
      .submit-btn {
        font-size: 1rem;
        padding: 0.8rem 0;
      }
    }
    .delete-problem-section {
      .delete-row {
        flex-direction: column;
        gap: 0.7rem;
        select, .delete-btn {
          width: 100%;
        }
      }
    }
  }
  @media (max-width: 400px) {
    .add-problem-form {
      padding: 0.7rem 0.2rem 0.5rem 0.2rem;
      h2 {
        font-size: 1.05rem;
      }
    }
    .delete-problem-section {
      h3 {
        font-size: 0.95rem;
      }
    }
  }
}
