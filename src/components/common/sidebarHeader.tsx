export default function SidebarHeader({
  effectivelyExpanded,
  collapsed,
  hovering,
  setCollapsed,
}: {
  effectivelyExpanded: boolean;
  collapsed: boolean;
  hovering: boolean;
  setCollapsed: (value: boolean) => void;
}) {
  return (
    <div
      className={`flex justify-between items-center pt-5 pb-6 border-gray-200 ${
        effectivelyExpanded ? "px-4" : ""
      }`}
    >
      <a href="/" className="flex items-center">
        <img
          src="/sample-a2svhub-logo.svg"
          alt="A2SV Hub Logo"
          width={effectivelyExpanded ? 100 : 80}
          height={effectivelyExpanded ? 40 : 24}
          style={{
            minWidth: 50,
            minHeight: effectivelyExpanded ? 40 : 24,
            maxWidth: 100,
            maxHeight: effectivelyExpanded ? 40 : 24,
            objectFit: "contain",
            transition: "all 0.3s",
            display: "block",
          }}
        />
      </a>
      {/* Collapse button*/}
      {!collapsed && (
        <button
          onClick={() => setCollapsed(true)}
          className="p-2 rounded-full hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800"
          aria-label="Collapse sidebar"
        >
          <img src="/chevron-left.svg" alt="Collapse" className="w-5 h-5" />
        </button>
      )}
      {/* Expand button  */}
      {collapsed && hovering && (
        <button
          onClick={() => setCollapsed(false)}
          className="p-2 rounded-full hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800"
          aria-label="Expand sidebar"
        >
          <img
            src="/chevron-left.svg"
            alt="Expand"
            className="w-5 h-5 transform rotate-180"
          />
        </button>
      )}
    </div>
  );
}
