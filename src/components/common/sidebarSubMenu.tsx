export default function SidebarSubMenu({
  items,
  pathname,
}: {
  items: Array<{
    name: string;
    href: string;
    count?: number;
  }>;
  pathname: string;
}) {
  return (
    <div className="ml-10 mt-1 relative">
      <div
        className="absolute left-2 top-0 h-full w-px bg-gray-300"
        style={{ transform: "translateX(-16px)" }}
      />
      {items.map((sub) => {
        const isSubActive = pathname.startsWith(sub.href);
        return (
          <div
            key={sub.name}
            className={`rounded-md ${
              isSubActive ? "bg-[rgb(0,171,85)]/10" : "hover:bg-gray-100"
            }`}
          >
            <a
              href={sub.href}
              className="flex items-center justify-between gap-2 p-2 pl-3 rounded text-sm transition-colors"
            >
              <p
                className={`${
                  isSubActive
                    ? "text-[rgb(0,171,85)] font-semibold"
                    : "text-gray-600"
                }`}
              >
                {sub.name}
              </p>
              {sub.count && (
                <p className="bg-gray-200 rounded-full p-1 px-3 w-fit text-center text-bold">
                  {sub.count}
                </p>
              )}
            </a>
          </div>
        );
      })}
    </div>
  );
}
