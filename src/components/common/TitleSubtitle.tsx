import React from "react";
import { TitleSubtitleProps } from "../../types";

const TitleSubtitle = ({title, subtitle, align = 'left', className = ''}: TitleSubtitleProps) => {
    const alignment = align === 'center' ? 'text-center' : align === 'right' ? 'text-right' : 'text-left';

    return (
        <div className={`mb-6 ${alignment} ${className}`}>
            <h1 className="text-2xl md:text-3xl font-bold">{title}</h1>
            {subtitle && <p className="text-md md:text-lg text-gray-600">{subtitle}</p>}
        </div>
    );
};

export default TitleSubtitle;