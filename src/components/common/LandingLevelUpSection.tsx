import React from "react";
import Image from "next/image";

const features = [
    {
        title: "Wide range pool of problems",
        description:
            "Wide pool of problems waiting for you. Wide pool of problems waiting for you.",
        image: "/images/wide-problems.png",
    },
    {
        title: "Comprehensive Roadmap",
        description:
            "Chart Your Course: Navigating Success Through Our Comprehensive Roadmap Feature.",
        image: "/images/comprehensive-roadmap.png",
    },
    {
        title: "Progress Tracker",
        description:
            "Stay On Course: Effortlessly Monitor Your Journey with Our Progress Tracker Feature",
        image: "/images/progress-tracker.png",
    },
    {
        title: "Contest Ratings",
        description:
            "Evaluate and Excel: Harness the Power of Contest Ratings for Continuous Improvement.",
        image: "/images/contest-ratings.png",
    },
];

const LandingLevelUpSection = () => {
    return (
        <section className="py-20 bg-gradient-to-b from-gray-50 to-white">
            <div className="container mx-auto px-4 max-w-7xl">
                <div className="text-center mb-16 max-w-4xl mx-auto">
                    <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                        Level up your education phase a step <span className="text-primary-600">ahead</span>
                    </h2>
                    <p className="text-lg md:text-xl text-gray-600">
                        Elevate Your Learning Journey: Discover the Next Level of Education with Enhanced Features
                    </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                    {features.map((feature, index) => (
                        <div key={index} className="group">
                            <div className="h-full bg-white rounded-xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
                                <div className="w-16 h-16 flex items-center justify-center mb-6 bg-primary-50 rounded-lg">
                                    <Image
                                        src={feature.image}
                                        alt={feature.title}
                                        width={40}
                                        height={40}
                                        className="w-10 h-10 object-contain"
                                        priority
                                    />
                                </div>
                                <h3 className="text-xl font-bold text-gray-900 mb-3">
                                    {feature.title}
                                </h3>
                                <p className="text-gray-600 leading-relaxed">
                                    {feature.description}
                                </p>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default LandingLevelUpSection;
