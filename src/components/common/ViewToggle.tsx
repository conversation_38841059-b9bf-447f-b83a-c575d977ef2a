// src/components/common/ViewToggle.tsx

import React from "react";
import { ViewToggleProps } from "@/types/index";

const ViewToggle: React.FC<ViewToggleProps> = ({ viewMode, onViewChange }) => {
  return (
    <div className="flex rounded-lg border border-gray-300 bg-gray-50 p-1">
      <button
        onClick={() => onViewChange("card")}
        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
          viewMode === "card"
            ? "bg-green-100 text-green-500 shadow-sm"
            : "text-gray-500 hover:text-gray-700"
        }`}
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
        </svg>
      </button>
      <button
        onClick={() => onViewChange("list")}
        className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
          viewMode === "list"
            ? "bg-green-100 text-green-500 shadow-sm"
            : "text-gray-500 hover:text-gray-700"
        }`}
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path
            fillRule="evenodd"
            d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
            clipRule="evenodd"
          />
        </svg>
      </button>
    </div>
  );
};

export default ViewToggle;
