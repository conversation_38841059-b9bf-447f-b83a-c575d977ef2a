// SidebarNavSection.tsx
"use client";
import { NavItem } from "./navitem";
import ProfileSection from "./sideProfileSection";
import SidebarHeadSection from "./sidebarHeadSection";
const menuItems = [
  { name: "Home", href: "/", icon: "/home.svg" },
  {
    name: "Tracks",
    href: "/tracks",
    icon: "/tracks.svg",
    submenu: [
      {
        name: "Progress",
        href: "/progress",
        count: 43,
      },
    ],
  },
  { name: "Problems", href: "/problems", icon: "/problems.svg" },
  {
    name: "Contests",
    href: "/contests",
    icon: "/contests.svg",
    submenu: [
      {
        name: "Upsolve",
        href: "/upsolve",
        count: 45,
      },
    ],
  },
  { name: "Roadmap", href: "/roadmap", icon: "/roadmap.svg" },
  { name: "Users", href: "/users", icon: "/users.svg" },
  { name: "Groups", href: "/groups", icon: "/groups.svg" },
  { name: "Forum", href: "/forum", icon: "/forum.svg" },
  { name: "Sessions", href: "/sessions", icon: "/sessions.svg" },
];

export default function SidebarNavSection({
  effectivelyExpanded,
  pathname,
  collapsed,
  hovering,
  openSubmenu,
  setOpenSubmenu,
}: {
  effectivelyExpanded: boolean;
  collapsed: boolean;
  hovering: boolean;
  pathname: string;
  openSubmenu: string | null;
  setOpenSubmenu: (value: string | null) => void;
}) {
  return (
    <>
      <ProfileSection
        effectivelyExpanded={effectivelyExpanded}
        collapsed={collapsed}
        hovering={hovering}
      />

      <nav className="flex flex-col py-2">
        {menuItems.map((item) => {
          const isParentActive = pathname === item.href;
          const hasActiveChild = item.submenu
            ? item.submenu.some((sub) => pathname.startsWith(sub.href))
            : false;
          const shouldShowSubmenu =
            item.submenu &&
            (openSubmenu === item.name || hasActiveChild) &&
            effectivelyExpanded;

          return (
            <NavItem
              key={item.name}
              item={item}
              isParentActive={isParentActive}
              hasActiveChild={hasActiveChild}
              shouldShowSubmenu={!!shouldShowSubmenu}
              effectivelyExpanded={effectivelyExpanded}
              openSubmenu={openSubmenu}
              setOpenSubmenu={setOpenSubmenu}
              pathname={pathname}
            />
          );
        })}
      </nav>

      {(!collapsed || effectivelyExpanded) && (
        <SidebarHeadSection
          effectivelyExpanded={effectivelyExpanded}
          pathname={pathname}
        />
      )}
    </>
  );
}
