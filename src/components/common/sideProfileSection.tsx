export default function SidebarProfileSection({
  effectivelyExpanded,
  collapsed,
  hovering,
}: {
  effectivelyExpanded: boolean;
  collapsed: boolean;
  hovering: boolean;
}) {
  return (
    <>
      {(effectivelyExpanded || hovering) && (
        <div className="bg-gray-100 rounded-xl py-2">
          <a href="/profile">
            <div className="flex items-center gap-3 p-3">
              <img
                src="/profile-placeholder.png"
                alt="Profile"
                className="w-10 h-10 rounded-full object-cover"
              />
              <div className="flex flex-col justify-between align-middle ml-3">
                <p className="text-sm font-semibold">Student Name</p>
                <p className="text-sm text-gray-600">Student</p>
              </div>
            </div>
          </a>
        </div>
      )}
      {collapsed && !hovering && (
        <img
          src="/profile-placeholder.png"
          alt="Profile"
          className="w-10 h-10 rounded-full object-cover my-10 items-center mx-auto"
        />
      )}
      {(!collapsed || effectivelyExpanded) && (
        <p className="uppercase text-xs items-center pt-10 pl-5 font-bold leading-tight tracking-wide">
          student
        </p>
      )}
    </>
  );
}
