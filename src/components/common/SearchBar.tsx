// src/components/common/SearchBar.tsx
import React from "react";
import { SearchBarProps } from "@/types/index";
import CloseIcon from "../../../public/icons/closeicon.svg";
import SearchGlass from "../../../public/icons/searchglass.svg";
const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = "Search user...",
}) => {
  const handleClear = () => {
    onChange("");
  };
  return (
    <div className="relative flex-3 max-w-md">
      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        {/* search glass icon found inside the search bar*/}
        {/* <Image
          src={searchglass}
          alt="Search icon"
          className="w-5 h-5 text-gray-400"
          width={20}
          height={20}
        /> */}
        <SearchGlass  className="w-6 h-6 "/>
        
      </div>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-green-500 focus:border-green-500"
        placeholder={placeholder}
      />
      {value && ( 
        <button
          type="button"
          onClick={handleClear}
          className="absolute inset-y-0 right-0 pr-3 flex items-center "
          aria-label="Clear search"
        >
          <div className="rounded-full focus:cursor-pointer hover:bg-gray-200 transition-colors p-1">
         
            <CloseIcon className="w-6 h-6"/>
          </div>
        </button>
      )}
    </div>
  );
};

export default SearchBar;
