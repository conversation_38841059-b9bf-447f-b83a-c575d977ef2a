export default function Topbar() {
  return (
    <header className="h-16 bg-white flex items-center justify-between px-6 pr-10 ">
      <button className="pl-5  rounded-full hover:bg-gray-100 hover:p-2">
        <img
          src="/search.svg"
          alt="Search Icon"
          className="h-6 w-6 "
          style={{
            filter:
              "invert(60%) sepia(6%) saturate(0%) hue-rotate(175deg) brightness(90%) contrast(87%)",
          }}
        />
      </button>
      <div className="flex items-center gap-8">
        <button
          className="transition-transform duration-200 hover:scale-105 rounded-full hover:bg-gray-100 hover:p-2"
          type="button"
        >
          <img src="/stark.svg" alt="stark Icon" className="h-6 w-6" />
        </button>
        <button className="relative transition-transform duration-200 rounded-full hover:scale-105 hover:bg-gray-100 hover:p-2">
          <img
            src="/bell.svg"
            alt="Notification Icon"
            className="h-6 w-6 "
            style={{
              filter:
                "invert(60%) sepia(6%) saturate(0%) hue-rotate(175deg) brightness(90%) contrast(87%)",
            }}
          />
        </button>
        <img
          src="/profile.jpg"
          alt="Profile"
          className="h-8 w-8 rounded-full object-cover"
        />
      </div>
    </header>
  );
}
