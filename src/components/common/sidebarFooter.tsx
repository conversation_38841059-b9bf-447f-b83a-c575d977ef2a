export default function SidebarFooter({
  effectivelyExpanded,
}: {
  effectivelyExpanded: boolean;
}) {
  if (!effectivelyExpanded) return null;

  return (
    <div className="space-y-3 py-4 pt-10">
      <img
        src="/feedback.svg"
        alt="Feedback"
        className="w-full h-fit mx-auto pb-4"
      />
      <p className="text-md text-center font-semibold">Hi, Doe</p>
      <p className="text-sm text-center text-gray-500 leading-relaxed">
        Spot any bugs or have <br />
        feedback?
      </p>
      <button
        className="bg-[rgb(0,171,85)] hover:bg-[rgb(47,109,79)] h-10 text-white font-semibold px-4 py-1 rounded-lg text-sm  mt-2 mx-auto block"
        style={{
          boxShadow: "0 4px 16px 0 rgba(0,171,85,0.3)",
        }}
      >
        <a href="">Contact Developer</a>
      </button>
      <div className="h-10"></div>
    </div>
  );
}
