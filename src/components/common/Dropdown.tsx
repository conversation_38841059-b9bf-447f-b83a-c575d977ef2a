// src/components/common/Dropdown.tsx
import React, { useState } from "react";
// import { DropdownOption } from "@/types/index";
import Image from "next/image";
import DropDownArrow from "../../../public/icons/dropdownarrow.svg";

interface DropdownOption {
  value: string | number;
  label: string;
}

interface  DropdownProps {
  label?: string;
  value: string | number | null;
  options: DropdownOption[];
  onChange: (value: string | number) => void;
  placeholder?: string;
  error?: string;
}
const Dropdown: React.FC<DropdownProps> = ({
  value,
  onChange,
  options,
  error,
  placeholder = "Select option",
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (optionValue: string) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  const selectedOption = options.find((option) => option.value === value);

  return (
    <div className="relative ">
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="relative w-full bg-white border border-gray-300 rounded-lg shadow-sm pl-3 pr-10 py-2 z-40 text-left cursor-pointer hover:ring-1 ring-black ring-opacity-5 hover:cursor-pointer focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-green-500 min-w-[120px]"
      >
        <span className="block truncate text-gray-900">
          {selectedOption ? selectedOption.label : placeholder}
        </span>
        <span className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
      
          <DropDownArrow  className="w-6 h-6" />
        </span>
      </button>
      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base  overflow-auto focus:outline-none sm:text-sm">
          {options.map((option) => (
            <button
              key={option.value}
              onClick={() => handleSelect(option.value)}
              className={`w-full text-left px-4 py-2 hover:bg-gray-100 ${
                value === option.value
                  ? "bg-green-50 text-green-600"
                  : "text-gray-900"
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}{" "}
      <div>{error && <p className="mt-1 text-sm text-red-600">{error}</p>}</div>
    </div>
  );
};

export default Dropdown;
