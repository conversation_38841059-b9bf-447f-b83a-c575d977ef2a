import SidebarSubMenu from "./sidebarSubMenu";
import Link from "next/link";
import Image from "next/image";
export function NavItem({
  item,
  isParentActive,
  hasActiveChild,
  shouldShowSubmenu,
  effectivelyExpanded,
  openSubmenu,
  setOpenSubmenu,
  pathname,
}: {
  item: {
    name: string;
    href: string;
    icon: string;
    submenu?: Array<{
      name: string;
      href: string;
      count?: number;
    }>;
  };
  isParentActive: boolean;
  hasActiveChild: boolean;
  shouldShowSubmenu: boolean;
  effectivelyExpanded: boolean;
  openSubmenu: string | null;
  setOpenSubmenu: (value: string | null) => void;
  pathname: string;
}) {
  return (
    <div key={item.name} className="relative my-1">
      <div
        className={`rounded-md ${
          isParentActive ? "bg-[rgb(0,171,85)]/10" : "hover:bg-gray-100"
        }`}
      >
        <Link
          href={item.href}
          className={`flex items-center gap-3 p-3 pl-2 mx-3 rounded-lg text-gray-600 transition-all duration-200 ${
            !effectivelyExpanded ? "justify-center" : ""
          }`} 
        >
          <div className="w-5 h-5 flex-shrink-0">
            <img
              src={item.icon}
              alt={item.name + " icon"}
              className="w-5 h-5"
              style={{
                filter: isParentActive
                  ? "invert(41%) sepia(98%) saturate(749%) hue-rotate(86deg) brightness(97%) contrast(101%)"
                  : "invert(34%) sepia(6%) saturate(0%) hue-rotate(175deg) brightness(95%) contrast(87%)",
              }}
            />
          </div>

          {effectivelyExpanded && (
            <>
              <span
                className={`whitespace-nowrap overflow-hidden text-sm items-center ${
                  isParentActive
                    ? "text-[rgb(0,171,85)] font-bold"
                    : "text-gray-600"
                }`}
              >
                {item.name}
              </span>
              {item.submenu && (
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                    setOpenSubmenu(
                      openSubmenu === item.name ? null : item.name
                    );
                  }}
                  className="p-1 ml-auto rounded-full hover:bg-gray-200"
                >
                  <img
                    src="/right-arrow.svg"
                    alt="Expand"
                    className={`w-4 h-4 transition-transform duration-200 ${
                      shouldShowSubmenu ? "rotate-[90deg]" : ""
                    }`}
                  />
                </button>
              )}
            </>
          )}
        </Link>
      </div>

      {shouldShowSubmenu && (
        <SidebarSubMenu items={item.submenu || []} pathname={pathname} />
      )}
    </div>
  );
}
