// src/components/users/UsersContainer.tsx
import React from "react";
import { useUsers } from "@/hooks/useUsers";
import SearchBar from "@/components/common/SearchBar";
import Dropdown from "@/components/common/Dropdown";
import ViewToggle from "@/components/common/ViewToggle";
import UserCard from "./UserCard";
import UserList from "./UserList";
import Image from "next/image";
import searchglass from "../../../public/icons/searchglass.svg";
import { UsersContainerProps } from "@/types";

const UsersContainer: React.FC<UsersContainerProps> = ({
  users,
  groupOptions,
  type = "users",
}) => {
  const {
    filteredUsers,
    searchTerm,
    selectedGroup,
    viewMode,
    handleSearchChange,
    handleGroupChange,
    handleViewModeChange,
  } = useUsers(users);
  if (type !== "users") {
    return (
      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <h2 className="text-xl font-semibold text-gray-700 mb-4">
          {type === "groups" ? "Groups" : "Countries"}
        </h2>
        <p className="text-gray-500">
          {type === "groups" ? "Groups" : "Countries"} functionality coming
          soon...
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <div className="flex  flex-col sm:flex-row gap-4 items-start sm:items-center justify-between p-4">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center flex-1">
          <ViewToggle viewMode={viewMode} onViewChange={handleViewModeChange} />
          <SearchBar
            value={searchTerm}
            onChange={handleSearchChange}
            placeholder="Search user..."
          />
        </div>
        <Dropdown
          value={selectedGroup}
          onChange={handleGroupChange}
          options={groupOptions}
          placeholder="All Groups"
        />
      </div>
      {/* Card Content */}
      <div>
        {viewMode === "card" ? (
         
          <div className="flex flex-wrap md:justify-center flex-col md:flex-row  gap-6">
            {filteredUsers.map((user) => (
              <UserCard key={user.id} user={user} />
            ))}
          </div>
        ) : (
          <UserList users={filteredUsers} />
        )}
      </div>

      {/* Empty State */}
      {filteredUsers.length === 0 && (
        <div className=" flex items-center justify-center flex-col   w-full">
          <Image
            src={searchglass}
            alt="No Result"
            className="w-20 h-20  text-gray-100 transition-colors "
          ></Image>
          <div className="text-black-300 text-lg font-bold">No Users Found</div>
          <div className="text-gray-400 text-sm mt-2">
            Try adjusting your search or filter criteria
          </div>
        </div>
      )}
    </div>
  );
};

export default UsersContainer;
