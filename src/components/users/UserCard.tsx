import React from "react";
import { UserCardProps } from "@/types/index";
import { getBadgeIcon, getInitials } from "../../utils/constants";
import Link from "next/link";
import Image from "next/image";

const UserCard: React.FC<UserCardProps> = ({ user }) => {
  return (
    <div className="bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 group  relative">
      {/* Background Header with Silhouette Effect */}
      <div className="relative h-32 overflow-hidden rounded-t-xl">
        {user.imageString ? (
          <>
            <Image
              src={user.imageString}
              alt="Background Silhouette"
              fill
              // width={500}
              // height={500}
              objectFit="cover"
              className="z-0"
            />
            {/* Greenish Overlay */}
            <div className="absolute inset-0 bg-green-500 opacity-70 z-10"></div>
          </>
        ) : (
          // Fallback if no image string
          <div className="h-full w-full bg-gradient-to-br from-emerald-400 via-green-500 to-teal-600"></div>
        )}
      </div>

      {/* Avatar  */}
      <div className="absolute top-25 left-1/2 transform -translate-x-1/2 z-20">
        <div className="w-15 h-15 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-300">
          {user.imageString ? (
            <Image
              src={user.imageString}
              alt={user.name}
              // fill
              //  width={500}
              // height={500}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-br from-amber-100 to-orange-100 flex items-center justify-center text-gray-700 font-semibold text-sm">
              {getInitials(user.name)}
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="pt-8 pb-4 px-2 text-center">
        {/* Name and Title */}
        <Link href="#">
          <div className="font-medium hover:cursor-pointer text-gray-800 hover:underline">
            {user.name}
          </div>
        </Link>
        <p className="text-sm font-medium text-gray-500 mb-4">
          {user.title} •{" "}
          <span className="font-medium text-gray-500">{user.group}</span>
        </p>

        {/* Social Badges */}
        <div className="flex justify-center space-x-4 m-1 py-2">
          {user.badges.map((badge, index) => (
            <div
              key={index}
              className="w-8 h-8 bg-gray-50 hover:bg-gray-100 rounded-full flex items-center justify-center text-sm transition-colors duration-200 border border-gray-200"
              title={badge}
            >
              <Image
                src={getBadgeIcon(badge)}
                alt="Badge icon"
                className="w-5 h-5  transition-colors"
                width={20}
                height={20}
              />
            </div>
          ))}
        </div>
        {/* border underline */}
        <div className="border-b-dashed border-b-1 border-gray-300"></div>
        {/* card stats */}
        <div className="flex  md:flex-row gap-4 text-center text-sm p-x-3">
          <div className="flex-1">
            <p className="text-gray-400">Problems</p>
            <div className="font-semibold text-gray-900">{user.problems}</div>
          </div>
          <div className="flex-1">
            <p className="text-gray-400">Submissions</p>
            <div className="font-semibold text-gray-900">
              {user.submissions}
            </div>
          </div>
          <div>
            <div className="text-gray-400 sm:text-nowrap">Dedicated Time</div>
            <div className="font-semibold text-gray-900">
              {user.dedicatedTime}
            </div>
          </div>
        </div>
        {/* View Profile Button */}
        <button className="w-full hover:bg-green-100 text-green-600 py-2.5 px-4 rounded-lg font-medium transition-colors duration-200 hover:cursor-pointer">
          View Profile
        </button>
      </div>
    </div>
  );
};

export default UserCard;
