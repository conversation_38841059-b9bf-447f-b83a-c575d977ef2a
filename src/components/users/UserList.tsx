// src/components/users/UserList.tsx
import React from "react";
import { UserListProps } from "@/types/index";
import Image from "next/image";
const UserList: React.FC<UserListProps> = ({ users }) => {
  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {/* Header */}
      <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
        <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
          <div className="col-span-4">Person</div>
          <div className="col-span-2 text-center">Solved</div>
          <div className="col-span-2 text-center">Time spent</div>
          <div className="col-span-2 text-center">Rating</div>
          <div className="col-span-2"></div>
        </div>
      </div>

      {/* User Rows */}
      <div className="divide-y divide-gray-200">
        {users.map((user) => (
          <div
            key={user.id}
            className="px-6 py-4 hover:bg-gray-50 transition-colors"
          >
            <div className="grid grid-cols-12 gap-4 items-center">
              {/* Person */}
              <div className="col-span-4 flex items-center space-x-3">
              
                  <div className=" rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold">    
                  
                        {/* Avatar  */}
                       
                          <div className="w-15 h-15 rounded-full overflow-hidden group-hover:scale-105 transition-transform duration-300">
                            {user.imageString ? (
                              <Image
                                src={user.imageString}
                                alt={user.name}
                                // fill
                                className="w-full h-full object-cover"
                              />
                            ) : (
                         <div className="w-full h-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold">
                    {user.name.charAt(0)}
                  </div>
                            )}
                          </div>
                        
                </div>
                <div>
                  <div className="font-medium text-gray-900">{user.name}</div>
              
                </div>
              </div>

              {/* Solved */}
              <div className="col-span-2 text-center">
                <div className="font-medium text-gray-900">{user.problems}</div>
              </div>

              {/* Time spent */}
              <div className="col-span-2 text-center">
                <div className="font-medium text-gray-900">
                  {user.dedicatedTime}
                </div>
              </div>

              {/* Rating */}
              <div className="col-span-2 text-center">
                <div className="font-medium text-gray-900">
                  {user.rating.toLocaleString()}
                </div>
              </div>

             
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default UserList;
