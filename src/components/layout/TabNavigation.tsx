// src/components/layout/TabNavigation.tsx

import React from "react";
import { TabNavigationProps } from "@/types/index";

const TabNavigation: React.FC<TabNavigationProps> = ({
  activeTab,
  onTabChange,
}) => {
  const tabs = [
    { id: "users" as const, label: "Users" },
    { id: "groups" as const, label: "Groups" },
    { id: "countries" as const, label: "Countries" },
  ];

  return (
    <div className="border-b border-gray-200 ">
      <nav className="flex   space-x-4 px-6">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`py-4 px-1 flex-1 hover:cursor-pointer border-b-2 font-medium text-sm transition-colors ${
              activeTab === tab.id
                ? "border-green-500"
                : "border-transparent text-gray-500 hover:text-gray-700  hover:border-gray-300"
            }`}
          >
            {tab.label}
          </button>
        ))}
      </nav>
    </div>
  );
};

export default TabNavigation;
