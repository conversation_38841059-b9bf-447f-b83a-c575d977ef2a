
interface ForumPost {
  id: string
  author: {
    name: string
    avatar: string
    initials: string
  }
  timestamp: string
  title: string
  content: string
  tags: string[]
  votes: number
  comments: number
}

const forumPosts: ForumPost[] = [
  {
    id: "1",
    author: {
      name: "<PERSON><PERSON><PERSON>",
      avatar: "/placeholder.svg?height=40&width=40",
      initials: "BT",
    },
    timestamp: "22 Apr 2025 1:20 PM",
    title: "Test",
    content: "Test",
    tags: ["Fun", "Question"],
    votes: 0,
    comments: 2,
  },
  {
    id: "2",
    author: {
      name: "<PERSON><PERSON> Mulug<PERSON> Gonfa",
      avatar: "/placeholder.svg?height=40&width=40",
      initials: "DM",
    },
    timestamp: "25 Feb 2025 11:03 AM",
    title: "Codeforces - 155A ~ [Rating - 800 (Easy)]",
    content: "",
    tags: ["Fun", "Question"],
    votes: 0,
    comments: 0,
  },
]
export default forumPosts