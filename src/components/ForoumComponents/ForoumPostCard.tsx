import { <PERSON>, CardContent, CardHeader } from "@/components/ForoumComponents/ForoumMessageCard"

import {  Plus } from "lucide-react"
 import forumPosts  from "@/components/ForoumComponents/ForoumSample"

export default function ForoumPostCard  () {
  return (
    <div>
      <div className="px-6 pb-6 space-y-4">
        {forumPosts.map((post) => (
          <Card key={post.id} className="bg-white shadow-[0_2px_8px_0_rgba(0,0,0,0.08)] ">
            <CardHeader className="pb-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-slate-600 flex items-center justify-center text-white text-lg font-semibold">
                  {post.author.initials}
                </div>
                <div>
                  <h3 className="text-[25px] text-slate-800">{post.author.name}</h3>
                  <p className="text-[20px] text-slate-600">{post.timestamp}</p>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              <div>
                <h2 className="text-xl font-semibold text-slate-900">{post.title}</h2>
                {post.content && <p className="text-slate-600">{post.content}</p>}
              </div>

              {post.tags.length > 0 && (
                <div className="flex gap-2">
                  {post.tags.map((tag, index) => (
                    <div
                      key={index}
                      className="rounded-full border border-yellow-500 text-yellow-400 bg-yellow-500/10 px-3 py-1 text-sm font-medium flex items-center justify-center"
                    >
                      {tag}
                    </div>
                  ))}
                </div>
              )}

              <div className="flex items-center justify-between pt-4">
                <div className="flex items-center gap-4">
                  {/* Voting */}
                  <div className="flex items-center gap-1">
                    <div className="rounded-full  hover:bg-slate-600 p-1 cursor-pointer transition-colors">
                      <img src="/chovrionupButton.svg" alt="chevron down icon" className="h-8 w-8 " />
                    </div>
                    <span className="text-gray-500  text-2xl">{post.votes}</span>

                    <div className="rounded-full  hover:bg-slate-600 p-1 cursor-pointer transition-colors">
                      <img src="/chovriondownButton.svg" alt="chevron down icon" className="h-8 w-8" />
                    </div>
                  </div>

                  {/* Comments */}
                  <div className="flex items-center  pl-2 gap-6 text-slate-400">
                    <img src="/chovrioncomment.svg" alt="chevron comment  icon" className="h-7 w-7 " />
                    <span className="text-gray-500 font-bold text-2xl " >{post.comments} Comments</span>
                  </div>
                </div>

               <div className="rounded-full bg-slate-700 hover:bg-slate-600 p-1 cursor-pointer transition-colors"></div>
                  <Plus className="w-4 h-4 mr-2" />
                  Write A Comment
                </div>
              

            </CardContent>

          </Card>
        ))}
      </div>
    </div>
  )
}

