import React from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ForoumComponents/ForoumListSelect"

const DropDownMenu = () => {
  return (
    <div>
        <div className="flex  mt-10 items-center justify-between p-6 gap-4">
        <Select>
            <SelectTrigger className="max-w-100 h-[50px] bg-white border-slate-300  text-slate-400 text-4xl  [&>span]:text-xl">
            <SelectValue placeholder="Filter post..." />
            </SelectTrigger>

          <SelectContent className=" bg-white border-slate-600">
            <SelectItem value="all">All Posts</SelectItem>
            <SelectItem value="questions">Questions</SelectItem>
            <SelectItem value="discussions">Discussions</SelectItem>
          </SelectContent>
        </Select>

        <Select defaultValue="latest">
          
          <SelectTrigger className="max-w-50 h-[50px] bg-white border-slate-300    text-slate-800 text-4xl  [&>span]:text-xl">
            <SelectValue />
          </SelectTrigger>
          <SelectContent className="bg-white  border-slate-600">
            <SelectItem value="latest">Latest</SelectItem>
            <SelectItem value="oldest">Oldest</SelectItem>
            <SelectItem value="popular">Popular</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
    </div>
  )
}

export default DropDownMenu
