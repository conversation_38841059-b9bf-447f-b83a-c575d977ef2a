import { useProblems } from "@/hooks/useProblems";
import { ProblemsTable } from "@/components/ProblemsTable";
// import { useState } from 'react';
// import { useTableControls } from '@/hooks/useTableControls';

const LatestProblems = () => {
  const { problems, handleVote } = useProblems();
  //   const {filteredAndSortedProblems} = useTableControls(problems);

  const visibleColumns = {
    id: false,
    difficulty: true,
    name: true,
    tag: true,
    track: false,
    contest: false,
    solved: false,
    added: true,
    vote: true,
    link: true,
  };

  return (
    <div className=" bg-white rounded-xl shadow p-6 w-full">
      <p className="text-lg font-bold text-wrap mb-4 ">Latest Problems</p>
      <ProblemsTable
        problems={problems}
        visibleColumns={visibleColumns}
        handleVote={handleVote}
      />
    </div>
  );
};

export default LatestProblems;
