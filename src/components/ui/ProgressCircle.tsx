import React, { useState, useEffect } from "react";
import { ProgressCircleProps } from "../../types";

export default function ProgressCircle({
  total,
  solved = 0,
  available = 0,
  type,
  reverseAvailable = false,
  title,
  size = "md",
  className = "",
  color,
  secondaryColor,
  "aria-label": ariaLabel,
}: ProgressCircleProps) {
  const solvedPercent = total > 0 ? Math.round((solved / total) * 100) : 0;
  const availablePercent = total > 0 ? (available / total) * 100 : 0;
  const availableStroke = reverseAvailable
    ? 100 - availablePercent
    : availablePercent;

  // Animation state
  const [animatedPercent, setAnimatedPercent] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    setIsAnimating(true);
    const animationDuration = 1000;
    const stepTime = 16;
    const totalSteps = animationDuration / stepTime;
    const stepValue = solvedPercent / totalSteps;
    let currentStep = 0;

    const animationInterval = setInterval(() => {
      currentStep++;
      const newValue = Math.min(stepValue * currentStep, solvedPercent);
      setAnimatedPercent(newValue);

      if (currentStep >= totalSteps) {
        clearInterval(animationInterval);
        setIsAnimating(false);
      }
    }, stepTime);

    return () => clearInterval(animationInterval);
  }, [solvedPercent]);

  const sizeMap = {
    sm: { container: "w-16 h-16", text: "text-xs", percent: "text-xs" },
    md: { container: "w-24 h-24", text: "text-xs", percent: "text-xl" },
    lg: { container: "w-50 h-50", text: "text-sm", percent: "text-2xl" },
  };

  const strokeWidth = size === "sm" ? 2 : 3;
  const radius = 15.9155;
  const circumference = 2 * Math.PI * radius;
  const offset = circumference - (animatedPercent / 100) * circumference;

  return (
    <div className={`relative ${sizeMap[size].container} ${className}`}>
      <svg
        className="absolute top-0 left-0 w-full h-full transform -rotate-90"
        viewBox="0 0 36 36"
        aria-hidden="true"
      >
        <circle
          cx="18"
          cy="18"
          r={radius}
          fill="none"
          stroke="#f3f4f6"
          strokeWidth={strokeWidth}
        />

        <circle
          cx="18"
          cy="18"
          r={radius}
          fill="none"
          stroke={`url(#progressGradient-${color || "default"})`}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          style={{
            transition: isAnimating
              ? "none"
              : "stroke-dashoffset 0.3s ease-out",
          }}
        />

        <defs>
          <linearGradient
            id={`progressGradient-${color || "default"}`}
            x1="0"
            y1="0"
            x2="0"
            y2="1"
          >
            <stop offset="0%" stopColor={color || "#5be584"} />
            <stop offset="100%" stopColor={color ? `${color}CC` : "#00ab55"} />
          </linearGradient>
        </defs>
      </svg>

      <div className="absolute inset-0 flex flex-col items-center justify-center">
        {type === "percent" ? (
          <span className={`font-bold text-gray-900 ${sizeMap[size].percent}`}>
            {Math.round(animatedPercent)}%
          </span>
        ) : (
          <>
            {title && (
              <span
                className={`text-gray-500 font-bold ${sizeMap[size].text} mb-1`}
              >
                {title}
              </span>
            )}
            <div className="flex flex-col items-center">
              <span
                className={`font-bold text-gray-900 ${sizeMap[size].percent}`}
              >
                {total}
              </span>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
