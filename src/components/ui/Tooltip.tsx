import React, { useState } from "react";
import { TooltipProps } from "@/types";

export default function Tooltip({
  content,
  children,
  count,
  delay = 300,
  className = "",
}: TooltipProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [timeoutId, setTimeoutId] = useState<number | null>(null);

  const showTooltip = () => {
    if (timeoutId) {
      window.clearTimeout(timeoutId);
    }
    const id = window.setTimeout(() => setIsVisible(true), delay);
    setTimeoutId(id);
  };

  const hideTooltip = () => {
    if (timeoutId) {
      window.clearTimeout(timeoutId);
      setTimeoutId(null);
    }
    setIsVisible(false);
  };

  return (
    <div className="relative inline-block">
      <div
        onMouseEnter={showTooltip}
        onMouseLeave={hideTooltip}
        onFocus={showTooltip}
        onBlur={hideTooltip}
      >
        {children}
      </div>
      {isVisible && (
        <div
          className={`absolute z-50 px-2 py-1 text-xs font-medium text-white bg-gray-800 rounded-md shadow-sm ${className}`}
          style={{
            left: "50%",
            transform: "translateX(-50%)",
            bottom: "-28px",
            whiteSpace: "nowrap",
            pointerEvents: "none",
          }}
          role="tooltip"
          onMouseEnter={hideTooltip}
        >
          {content}
          {count !== undefined && ` (${count})`}
          <div
            className="absolute top-[-4px] left-1/2 transform -translate-x-1/2 border-4 border-b-gray-800 border-l-transparent border-r-transparent border-t-transparent"
            aria-hidden="true"
          />
        </div>
      )}
    </div>
  );
}
