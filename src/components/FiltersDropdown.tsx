import { X } from 'lucide-react';

interface FiltersDropdownProps {
  filters: any[];
  addFilter: () => void;
  removeFilter: (id: number) => void;
  newFilter: any;
  setNewFilter: (filter: any) => void;
}

export const FiltersDropdown = ({
  filters,
  addFilter,
  removeFilter,
  newFilter,
  setNewFilter
}: FiltersDropdownProps) => {
  return (
    <div className="absolute top-full left-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
      <div className="p-4">
        <div className="mb-4">
          <button className="text-xs text-gray-500 hover:text-gray-700 mb-3">
            Hide filters
          </button>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div>
              <label className="text-gray-500">Columns</label>
              <select 
                value={newFilter.column}
                onChange={(e) => setNewFilter({...newFilter, column: e.target.value})}
                className="w-full mt-1 px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option>Id</option>
                <option>Name</option>
                <option>Difficulty</option>
                <option>Tag</option>
              </select>
            </div>
            <div>
              <label className="text-gray-500">Operators</label>
              <select 
                value={newFilter.operator}
                onChange={(e) => setNewFilter({...newFilter, operator: e.target.value})}
                className="w-full mt-1 px-2 py-1 border border-gray-300 rounded text-sm"
              >
                <option>contains</option>
                <option>equals</option>
                <option>starts with</option>
              </select>
            </div>
            <div>
              <label className="text-green-600">Value</label>
              <input
                type="text"
                placeholder="Filter value"
                value={newFilter.value}
                onChange={(e) => setNewFilter({...newFilter, value: e.target.value})}
                onKeyPress={(e) => e.key === 'Enter' && addFilter()}
                className="w-full mt-1 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-1 focus:ring-green-500"
              />
            </div>
          </div>
        </div>
        
        {/* Active Filters */}
        {filters.map((filter) => (
          <div key={filter.id} className="flex items-center gap-2 mb-2 p-2 bg-gray-50 rounded">
            <button
              onClick={() => removeFilter(filter.id)}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="w-3 h-3" />
            </button>
            <span className="text-sm text-gray-700">
              {filter.column} {filter.operator} "{filter.value}"
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};