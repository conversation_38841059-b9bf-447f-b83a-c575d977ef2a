'use client';
import { Submission } from '@/types/submission';
import Image from 'next/image';

interface Props {
  submissions: Submission[];
}


export default function SubmissionTable({ submissions }: Props) {
  return (
    <div className="p-6 bg-white rounded-md shadow-md">
      <h2 className="text-xl font-semibold mb-4">Latest Submissions</h2>
      <table className="w-full table-auto border-collapse">
        <thead>
          <tr className="text-left border-b border-gray-400">
            <th className="p-3 text-lg font-semibold">Name</th>
            <th className="p-2 text-right">Problem</th>
            <th className="p-2 text-right">Time spent</th>
            <th className="p-2 text-right">Language</th>
            <th className="p-2 text-right">Added</th>
          </tr>
        </thead>
        <tbody>
          {submissions.map((s) => (
            <tr key={s.id} className="border-b border-gray-300 hover:bg-gray-50">
              <td className="p-2 flex items-center gap-2">
                {s.avatarUrl ? (
                  <Image
                    src={s.avatarUrl}
                    alt={s.name}
                    width={28}
                    height={28}
                    className="rounded-full"
                  />
                ) : (
                  <div className="w-7 h-7 bg-gray-300 rounded-full" />
                )}
                {s.name}
              </td>
              <td className="p-2 text-right">{s.problem}</td>
              <td className="p-2 text-right">{s.timeSpent}</td>
              <td className="p-2 text-right">{s.language}</td>
              <td className="p-2 text-right">{s.added}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
