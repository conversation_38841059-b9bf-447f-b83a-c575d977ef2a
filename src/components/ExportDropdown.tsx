interface ExportDropdownProps {
    options: Array<{ label: string; action: () => void }>;
  }
  
  export const ExportDropdown = ({ options }: ExportDropdownProps) => {
    return (
      <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
        <div className="py-1">
          {options.map((option, index) => (
            <button
              key={index}
              onClick={option.action}
              className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
    );
  };