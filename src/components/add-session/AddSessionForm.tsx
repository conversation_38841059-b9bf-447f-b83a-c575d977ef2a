import React, { useState } from "react";
import { DateCalendar } from "@mui/x-date-pickers/DateCalendar";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DemoContainer } from "@mui/x-date-pickers/internals/demo";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { TimePicker } from "@mui/x-date-pickers/TimePicker";
import dayjs, { Dayjs } from "dayjs";

const AddSessionForm = () => {
  const [lecturer, setLecturer] = useState("");
  const [fund, setFund] = useState("");
  const [name, setName] = useState("");
  const [sendCalendarInvites, setSendCalendarInvites] = useState(true);
  const [date, setDate] = useState<Dayjs | null>(null);
  const [selectedGroup, setSelectedGroup] = useState<string>("");
  const valid = name && date && selectedGroup;

  return (
    <div className="space-y-4">
      <input
        type="text"
        placeholder="Name*"
        value={name}
        onChange={(e) => setName(e.target.value)}
        className="w-full border rounded-md p-2 px-4 py-3 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
      />
      <input
        type="text"
        placeholder="Description"
        className="w-full border rounded-md p-2 px-4 py-3 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
      />
      <div className="w-full flex gap-2 justify-between">
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DateCalendar value={date} onChange={setDate} />
        </LocalizationProvider>
        <div className="w-1/2">
          <div className="flex flex-col gap-4">
            <div className="w-full ">
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DemoContainer components={["TimePicker"]}>
                  <TimePicker label="Start time" />
                </DemoContainer>
              </LocalizationProvider>
            </div>
            <div className="flex items-center  pl-2">
              <img src="/right-arrow.svg" className="w-4 h-4 rotate-180" />
              <span className="text-sm">+h</span>
              <img src="/right-arrow.svg" className="w-4 h-4" />
            </div>
            <div className="w-full ">
              <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DemoContainer components={["TimePicker"]}>
                  <TimePicker label="End time" />
                </DemoContainer>
              </LocalizationProvider>
            </div>
          </div>
          <div className="flex flex-wrap gap-2 py-3">
            {[
              "8:00 PM-11:30 PM",
              "8:30 PM-11:00 PM",
              "9:00 PM-11:00 PM",
              "8:00 AM-11:00 AM",
              "8:00 PM-8:00 PM",
              "9:00 PM-10:00 PM",
              "8:59 PM-11:00 PM",
              "8:00 PM-11:59 PM",
              "8:00 PM-11:06 PM",
              "8:00 PM-11:31 PM",
              "8:59 PM-11:30 PM",
              "9:00 PM-11:00 PM",
              "8:00 PM-9:00 PM",
            ].map((slot, index) => (
              <button
                key={index}
                className=" text-xs rounded-full px-1 py-1 bg-gray-200 hover:bg-green-100 transition"
              >
                {slot}
              </button>
            ))}
          </div>
        </div>
      </div>
      <div className="relative w-full ">
        <label
          className="absolute -top-2 left-2 bg-white  text-xs text-gray-500 z-10"
          htmlFor="super-group"
        >
          Select groups*
        </label>
        <select
          id="super-group"
          value={selectedGroup}
          onChange={(e) => setSelectedGroup(e.target.value)}
          className="w-full border border-gray-300 rounded px-4 p-2 pt-5 text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          <option value="">Select group</option>
          <option value="remote">Remote</option>
          <option value="camp-i">Camp I</option>
        </select>
      </div>
      <input
        type="text"
        placeholder="Location"
        className="w-full border rounded-md p-2 px-4 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
      />
      <div className="flex gap-4">
        <input
          type="text"
          placeholder="Resource link"
          className="w-full border rounded-md p-2 px-4 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
        />
        <input
          type="text"
          placeholder="Recording link"
          className="w-full border rounded-md p-2 px-4 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
        />
      </div>
      <div className="relative w-full text-gray-300">
        <select
          value={lecturer}
          onChange={(e) => setLecturer(e.target.value)}
          className="w-full border border-gray-300 rounded px-4 p-2  text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          <option value="">Lecturer</option>
          <option>Progress</option>
          <option>Python</option>
          <option>Camp I</option>
          <option>Remote</option>
        </select>
      </div>
      <div className="relative w-full text-gray-300">
        <select
          value={fund}
          onChange={(e) => setFund(e.target.value)}
          className="w-full border border-gray-300 rounded px-4 p-2  text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
        >
          <option value="">Fund</option>
          <option>Progress</option>
          <option>Python</option>
          <option>Camp I</option>
          <option>Remote</option>
        </select>
      </div>

      <label className="flex items-center cursor-pointer">
        <div className="relative">
          <input
            type="checkbox"
            checked={sendCalendarInvites}
            onChange={() => setSendCalendarInvites((v) => !v)}
            className="sr-only"
          />
          <div
            className={`block w-8 h-4 mb-1 rounded-full transition-colors duration-300 ${
              sendCalendarInvites ? "bg-green-500/60" : "bg-gray-400"
            }`}
          ></div>
          <div
            className={`dot absolute  bottom-0.5  w-5 h-5 rounded-full transition-transform duration-300 ${
              sendCalendarInvites ? "translate-x-4 bg-green-600" : "bg-white"
            }`}
          ></div>
        </div>
        <span className="ml-3 text-gray-700 font-medium">
          Send calendar invites
        </span>
      </label>
      <button
        className={`w-full  rounded-md p-3 font-semibold ${
          valid ? "bg-green-600 text-white" : "bg-gray-200 text-gray-400"
        }     cursor-pointer`}
        disabled
      >
        Submit
      </button>
    </div>
  );
};

export default AddSessionForm;
