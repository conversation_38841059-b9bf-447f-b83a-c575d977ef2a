interface ColumnsDropdownProps {
    visibleColumns: Record<string, boolean>;
    toggleColumn: (key: string) => void;
  }
  
  export const ColumnsDropdown = ({ visibleColumns, toggleColumn }: ColumnsDropdownProps) => {
    const columnOptions = [
      { key: 'id', label: 'Id' },
      { key: 'difficulty', label: 'Difficulty' },
      { key: 'name', label: 'Name' },
      { key: 'tag', label: 'Tag' },
      { key: 'track', label: 'Track' },
      { key: 'contest', label: 'Contest' },
      { key: 'solved', label: 'Solved' },
      { key: 'added', label: 'Added' },
      { key: 'vote', label: 'Vote' },
      { key: 'link', label: 'Link' }
    ];
  
    const handleHideAll = () => {
      Object.keys(visibleColumns).forEach(key => toggleColumn(key));
    };
  
    const handleShowAll = () => {
      Object.keys(visibleColumns).forEach(key => {
        if (!visibleColumns[key]) toggleColumn(key);
      });
    };
  
    return (
      <div className="absolute top-full left-0 mt-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
        <div className="p-3">
          <div className="mb-3">
            <label className="text-xs text-green-600 font-medium">Find column</label>
            <input
              type="text"
              placeholder="Column title"
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {columnOptions.map((column) => (
              <label key={column.key} className="flex items-center gap-2 text-sm cursor-pointer">
                <div className="relative">
                  <input
                    type="checkbox"
                    checked={visibleColumns[column.key]}
                    onChange={() => toggleColumn(column.key)}
                    className="sr-only"
                  />
                  <div className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                    visibleColumns[column.key] ? 'bg-green-500 border-green-500' : 'border-gray-300'
                  }`}>
                    {visibleColumns[column.key] && <div className="w-2 h-2 bg-white rounded-full"></div>}
                  </div>
                </div>
                <span className="text-gray-700">{column.label}</span>
              </label>
            ))}
          </div>
          <div className="flex gap-2 mt-4 pt-3 border-t border-gray-200">
            <button 
              onClick={handleHideAll}
              className="text-xs text-green-600 hover:text-green-700"
            >
              Hide All
            </button>
            <button 
              onClick={handleShowAll}
              className="text-xs text-green-600 hover:text-green-700"
            >
              Show All
            </button>
          </div>
        </div>
      </div>
    );
  };