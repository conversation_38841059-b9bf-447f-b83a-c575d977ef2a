import React from "react";
interface CardProps {
  title: string;
  group: string;
  members: string;
  timeSpent: number;
  aveRating: number;
}
const Card: React.FC<CardProps> = ({
  title,
  group,
  members,
  timeSpent,
  aveRating,
}) => {
  return (
    <div className="bg-white shadow-md rounded-2xl p-4 m-2 w-full h-72 border-[1px] border-gray-400 flex flex-col justify-between hover:border-none hover:bg-green-100">
      <div>
        <h2 className="font-bold text-lg">{title}</h2>
        <div className="flex items-center">
          <p className="text-gray-600 text-sm">{group}</p>
          <p className="mx-2 text-gray-400 text-lg">·</p>
          <p className="text-gray-600 text-sm">{members} Members</p>
        </div>
      </div>
      <div className="grid grid-cols-2 ">
        <div className="flex ">
          <div className="h-12 w-1 bg-gray-300 mr-3"></div>
          <div className="flex flex-col">
            <p className="text-sm">Time Spent</p>
            <p>{timeSpent.toLocaleString()}</p>
          </div>
        </div>
        <div className="flex ">
          <div className="h-12 w-1 bg-gray-300 mr-3"></div>
          <div className="flex flex-col">
            <p className="text-sm">Avg. Rating</p>
            <p>{aveRating.toLocaleString()}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Card;
