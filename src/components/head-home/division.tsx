import React from "react";
import SummaryHeader from "./summaryHeader";

const Division = () => {
  return (
    <div>
      <SummaryHeader
        title="Divsions"
        choices={[
          "Remote",
          "Inperson",
          "Rogue",
          "Ghana inperson",
          "Remote G5A",
          "Remote G5B",
        ]}
      />
      <div className="w-full border-[1px] border-gray-300 rounded-lg py-4 mt-4 ">
        <span className="font-semibold px-4">Div 1</span>
        <span className="text-sm">(0) </span>
        <hr className="border-gray-300" />
      </div>
      <div className="w-full border-[1px] border-gray-300 rounded-lg pt-4 pb-2 mt-4 ">
        <span className="font-semibold px-4">Div 2</span>{" "}
        <span className="text-sm">(3) </span>
        <hr className="border-gray-300" />
        <div className="">
          <div className="w-full gap-4 flex px-4 py-1">
            <img src={`profile`} className="w-6 h-6 rounded-full " />
            <p className="align-middle">John Doe</p>
          </div>
          <hr className="border-gray-300" />
        </div>
        <div className=" ">
          <div className="w-full gap-4 flex px-4 py-1">
            <img src={`profile`} className="w-6 h-6 rounded-full " />
            <p className="align-middle">Samuel Tesfaye</p>
          </div>
          <hr className="border-gray-300" />
        </div>
        <div className=" ">
          <div className="w-full gap-4 flex px-4 py-1">
            <img src={`profile`} className="w-6 h-6 rounded-full  " />
            <p className="align-middle">David</p>
          </div>
        </div>
      </div>
      <div className="w-full border-[1px] border-gray-300 rounded-lg py-4 mt-4 ">
        <span className="font-semibold px-4">Div 3</span>
        <span className="text-sm">(0) </span>
        <hr className="border-gray-300" />
      </div>
    </div>
  );
};

export default Division;
