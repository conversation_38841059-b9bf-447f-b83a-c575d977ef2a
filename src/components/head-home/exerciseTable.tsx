"use client";
import React from "react";

const users = [
  {
    name: "<PERSON>",
    score: "69|100%",
    avatar: "/samuel.jpg",
    data: [
      { repeat: 2, time: 20 },
      {},
      { repeat: 1, time: 30 },
      { repeat: 4, time: 25 },
      { repeat: 1, time: 10 },
    ],
    href: "/samuel",
  },
  {
    name: "<PERSON>",
    score: "69|100%",
    avatar: "/nathan.jpg",
    data: [
      {},
      { repeat: 2, time: 30 },
      { repeat: 5, time: 120 },
      { repeat: 6, time: 33 },
      { repeat: 1, time: 26 },
    ],
    href: "/nathan",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    score: "69|100%",
    avatar: "/samuel.jpg",
    data: [
      { repeat: 2, time: 20 },
      {},
      { repeat: 1, time: 30 },
      { repeat: 4, time: 25 },
      { repeat: 1, time: 10 },
    ],
    href: "/samuel",
  },
  {
    name: "<PERSON>",
    score: "69|100%",
    avatar: "/nathan.jpg",
    data: [
      {},
      { repeat: 2, time: 30 },
      { repeat: 5, time: 120 },
      { repeat: 6, time: 33 },
      { repeat: 1, time: 26 },
    ],
    href: "/nathan",
  },
  {
    name: "Anfal",
    score: "69|100%",
    avatar: "/samuel.jpg",
    data: [
      { repeat: 2, time: 20 },
      {},
      { repeat: 1, time: 30 },
      {},
      { repeat: 4, time: 25 },
      {},
      { repeat: 1, time: 10 },
      {},
    ],
    href: "/samuel",
  },
  {
    name: "Biniyam",
    score: "69|100%",
    avatar: "/nathan.jpg",
    data: [
      {},
      { repeat: 2, time: 30 },
      { repeat: 5, time: 120 },
      {},
      { repeat: 6, time: 33 },
      {},
      { repeat: 1, time: 26 },
      {},
    ],
    href: "/nathan",
  },
];

const exercises = [
  {
    title: "Vertical Order Traversal",
    source: "LeetCode",
    difficulty: "Medium",
    date: "24 May 24",
    solved: "62%",
    href: "https://leetcode.com/problems/vertical-order-traversal-of-a-binary-tree/",
  },
  {
    title: "Tower of Hanoi",
    source: "GeeksForGeeks",
    difficulty: "Medium",
    date: "24 May 24",
    solved: "62%",
    href: "https://leetcode.com/problems/vertical-order-traversal-of-a-binary-tree/",
  },
  {
    title: "Find in Mountain Array",
    source: "LeetCode",
    difficulty: "Medium",
    date: "24 May 24",
    solved: "62%",
    href: "https://leetcode.com/problems/vertical-order-traversal-of-a-binary-tree/",
  },
  {
    title: "Lowest Common Ancestor",
    source: "LeetCode",
    difficulty: "Medium",
    date: "24 May 24",
    href: "https://leetcode.com/problems/vertical-order-traversal-of-a-binary-tree/",
    solved: "62%",
  },
  {
    title: "Vertical Order Traversal",
    source: "LeetCode",
    difficulty: "Medium",
    date: "24 May 24",
    solved: "62%",
    href: "https://leetcode.com/problems/vertical-order-traversal-of-a-binary-tree/",
  },
  {
    title: "Tower of Hanoi",
    source: "GeeksForGeeks",
    difficulty: "Medium",
    date: "24 May 24",
    solved: "62%",
    href: "https://leetcode.com/problems/vertical-order-traversal-of-a-binary-tree/",
  },
  {
    title: "Find in Mountain Array",
    source: "LeetCode",
    difficulty: "Medium",
    date: "24 May 24",
    solved: "62%",
    href: "https://leetcode.com/problems/vertical-order-traversal-of-a-binary-tree/",
  },
  {
    title: "Lowest Common Ancestor",
    source: "LeetCode",
    difficulty: "Medium",
    date: "24 May 24",
    solved: "62%",
    href: "https://leetcode.com/problems/vertical-order-traversal-of-a-binary-tree/",
  },
];

export default function ExerciseTable() {
  return (
    <div className=" bg-white overflow-auto">
      <div className="flex ">
        {/* Fixed columns */}
        <div className="flex-shrink-0">
          <table className="border-separate border-spacing-0">
            <thead>
              <tr>
                <th className="w-12 px-4 py-2 text-left sticky left-0 z-20"></th>
                <th className="w-48 px-4 py-2 sticky left-0 z-20 flex text-end justify-end text-xl">
                  69
                </th>
              </tr>
            </thead>
            <tbody>
              {users.map((user, i) => (
                <tr
                  key={`${user.name}-${i}`}
                  className="group hover:underline cursor-pointer"
                  onClick={() => user.href && window.open(user.href)}
                >
                  <td className="w-12 px-4 py-2 sticky left-0 z-20 items-center group-hover:underline border-b border-gray-500">
                    <span className="group-hover:underline font-bold">
                      {i + 1}
                    </span>
                  </td>
                  <td className="w-48 px-4 py-1 sticky left-0 z-20 flex items-center gap-2 border-b border-gray-500">
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-6 h-6 rounded-full"
                    />
                    <div>
                      <div className="font-light group-hover:underline">
                        {user.name}
                      </div>
                      <div className="text-xs text-gray-400 group-hover:underline">
                        {user.score}
                      </div>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Scrollable exercise columns */}
        <div className="max-w-2xl overflow-x-auto">
          <table className="min-w-max">
            <thead>
              <tr>
                {exercises.map((ex, idx) => (
                  <th
                    key={idx}
                    className="w-48 px-4 py-2 text-left text-xs cursor-pointer group font-light"
                    onClick={() => ex.href && window.open(ex.href, "_blank")}
                  >
                    <div className="overflow-x-auto whitespace-nowrap border-r border-gray-500">
                      <div
                        className={`text-nowrap transition-all ${
                          ex.href ? "group-hover:underline" : ""
                        }`}
                      >
                        {ex.title}
                        <div
                          className={`text-xs text-nowrap flex ${
                            ex.difficulty === "Easy"
                              ? "text-green-500"
                              : ex.difficulty === "Medium"
                              ? "text-yellow-500"
                              : ex.difficulty === "Hard"
                              ? "text-red-500"
                              : "text-gray-400"
                          }`}
                        >
                          {ex.source}{" "}
                          <span className="text-gray-500 text-xs align-middle flex items-center">
                            <span className="text-md leading-none">·</span>
                            <span>{ex.date}</span>
                            <span className="text-md leading-none">·</span>
                            <span>{ex.solved}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {users.map((user, userIndex) => (
                <tr
                  key={`${user.name}-${userIndex}`}
                  className="hover:bg-gray-100"
                >
                  {user.data.map((cell, idx) => (
                    <td key={idx} className="w-48 px-4 h-12 ">
                      {cell.repeat !== undefined && cell.time !== undefined ? (
                        <div className="bg-[#5BE584] text-black rounded-md px-2 py-1 flex items-center gap-2 w-full text-left">
                          <span>⟳{cell.repeat}</span>
                          <span>⏱{cell.time} min</span>
                        </div>
                      ) : null}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
