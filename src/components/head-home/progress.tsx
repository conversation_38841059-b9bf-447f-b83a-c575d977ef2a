type ProgressProps = {
  value: number;
  color?: string;
  total: number;
  solved: number;
  available: number;
};

export function Progress({
  value,
  color = "#22C55E",
  total,
  solved,
  available,
}: ProgressProps) {
  return (
    <div className="">
      {" "}
      <p className="text-xl text-center font-semibold my-2">Team Completion</p>
      <div className="w-full h-6 bg-[#abf0c4] rounded-full overflow-hidden">
        <div
          className="h-full rounded-full transition-all duration-300"
          style={{
            width: `${value}%`,
            background: color,
          }}
        />
      </div>
      <p className="text-center my-3">
        {total} Exercises | {solved} Solved (average) | {value} Completion |{" "}
        {available} Available (average)
      </p>
    </div>
  );
}
