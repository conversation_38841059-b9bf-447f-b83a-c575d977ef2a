import React, { useState } from "react";

const ProblemSolvingHeader = () => {
  const [selectedMonth, setSelectedMonth] = useState<number>(
    new Date().getMonth()
  );

  const months = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  return (
    <div className="flex items-center justify-between">
      <div className="mb-2">
        <h2 className="text-lg font-bold">Problem Solving</h2>
        <p className="text-gray-500 text-sm">(-64%) than last month </p>
      </div>
      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
        <select
          value={selectedMonth}
          onChange={(e) => setSelectedMonth(Number(e.target.value))}
          className="text-sm font-semibold"
          style={{
            background: "#f3f4f6",
            border: "none",
            borderRadius: 6,
            padding: "6px 12px",
            fontSize: 12,
            fontWeight: 400,
            cursor: "pointer",
          }}
        >
          {months.map((month, idx) => (
            <option key={month} value={idx}>
              {month}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default ProblemSolvingHeader;
