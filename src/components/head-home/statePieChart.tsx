"use client";
import * as React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Label,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

const chartData = [
  { label: "Good", value: 73, color: "#54D62C" },
  { label: "Okay", value: 27, color: "#FACC15" },
  { label: "Bad", value: 0, color: "#EF4444" },
];

export default function StatePieChart() {
  const total = React.useMemo(
    () => chartData.reduce((acc, curr) => acc + curr.value, 0),
    []
  );
  const solved = chartData.find((d) => d.label === "Solved")?.value ?? 0;
  return (
    <div>
      <div className="flex-1 flex flex-col items-center justify-center">
        <ResponsiveContainer width={250} height={250}>
          <PieChart>
            <Pie
              data={chartData}
              dataKey="value"
              nameKey="label"
              cx="50%"
              cy="50%"
              innerRadius={50}
              outerRadius={80}
              stroke="#fff"
              startAngle={90}
              endAngle={-270}
              labelLine={false}
              label={({ percent, cx, cy, midAngle, outerRadius, index }) => {
                if (percent < 0.05) return null;
                const RADIAN = Math.PI / 180;
                const radius = outerRadius;
                const x = cx + radius * Math.cos(-midAngle * RADIAN) - 20;
                const y = cy + radius * Math.sin(-midAngle * RADIAN) - 40;
                return (
                  <text
                    x={x + 11}
                    y={y - 5}
                    fill="#fff"
                    textAnchor={x > cx ? "start" : "end"}
                    dominantBaseline="central"
                    fontSize={12}
                    fontWeight={600}
                  >
                    {(percent * 100).toFixed(0)}%
                  </text>
                );
              }}
            >
              {chartData.map((entry) => (
                <Cell key={entry.label} fill={entry.color} />
              ))}
              <Label
                position="center"
                content={({ viewBox }) => {
                  if (!viewBox) return null;
                  const { cx, cy } = viewBox;
                  return (
                    <>
                      <text
                        x={cx}
                        y={cy - 8}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="text-lg font-bold "
                        fill="#22C55E"
                      >
                        State
                      </text>
                      <text
                        x={cx}
                        y={cy + 24}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="text-3xl font-bold "
                      >
                        {solved}
                      </text>
                    </>
                  );
                }}
              />
            </Pie>
            <Tooltip
              formatter={(value: number, name: string) => [`${value}`, name]}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
      <hr className="border-gray-200 mb-4" />
      <div className="flex gap-4 ml-6 justify-center">
        {chartData.map((entry) => (
          <div key={entry.label} className="flex items-center gap-1">
            <span
              className="inline-block w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            ></span>
            <span className="text-xs text-gray-700">{entry.label}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
