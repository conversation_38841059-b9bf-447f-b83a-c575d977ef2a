"use client";
import React from "react";
import {
  <PERSON><PERSON>hart,
  Line,
  CartesianGrid,
  XAxis,
  <PERSON>A<PERSON>s,
  <PERSON>lt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
} from "recharts";
import type { Props as LegendContentProps } from "recharts/types/component/DefaultLegendContent";
import ProblemSolvingHeader from "./problemSolvingHeader";

const chartData = [
  { day: "Mon", week1: 1, week2: 2, week3: 3, week4: 4 },
  { day: "Tue", week1: 3, week2: 6, week3: 0, week4: 2 },
  { day: "Wed", week1: 2, week2: 4, week3: 5, week4: 6 },
  { day: "Thu", week1: 4, week2: 0, week3: 6, week4: 7 },
  { day: "Fri", week1: 0, week2: 6, week3: 7, week4: 0 },
  { day: "Sat", week1: 6, week2: 7, week3: 3, week4: 9 },
  { day: "Sun", week1: 6, week2: 7, week3: 3, week4: 9 },
];

const renderLegend = (props: LegendContentProps) => {
  const { payload } = props;
  return (
    <div
      className="mb-5 flex flex-wrap gap-x-4 gap-y-2 justify-end"
      style={{ rowGap: 8, columnGap: 16 }}
    >
      {payload?.map((entry) => (
        <div
          key={entry.value}
          className="flex items-center gap-1.5"
          style={{ minWidth: 80 }}
        >
          <span
            style={{
              display: "inline-block",
              width: 10,
              height: 10,
              borderRadius: "50%",
              background: entry.color,
            }}
          />
          <span style={{ color: "#4B5563", fontSize: 14 }}>{entry.value}</span>
        </div>
      ))}
    </div>
  );
};

const ProblemSolvingChart = () => {
  return (
    <div className="bg-white rounded-xl shadow p-6 w-full flex md:flex-2/3 flex-col">
      <ProblemSolvingHeader />
      <div className="flex flex-col items-center justify-center">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart
            data={chartData}
            margin={{ left: 6, right: 6, top: 16, bottom: 8 }}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="day"
              tickLine={false}
              axisLine={false}
              className="text-xs text-gray-500"
            />
            <YAxis
              allowDecimals={false}
              axisLine={false}
              tickLine={false}
              tick={{ fill: "#6B7280", fontSize: 12 }}
            />
            <Tooltip
              content={({ active, payload, label }) => {
                if (!active || !payload || payload.length === 0) return null;
                return (
                  <div
                    style={{
                      background: "#fff",
                      border: "1px solid #E5E7EB",
                      borderRadius: 8,
                      boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
                      minWidth: 120,
                    }}
                  >
                    <div
                      style={{
                        background: "#E5E7EB",
                        borderRadius: 4,
                        borderBottomLeftRadius: 0,
                        borderBottomRightRadius: 0,
                        padding: "4px 8px",
                        marginBottom: 8,
                        color: "#6B7280",
                        fontWeight: 500,
                        fontSize: 14,
                        textAlign: "center",
                      }}
                    >
                      {label}
                    </div>
                    {payload.map((entry) => (
                      <div
                        key={entry.dataKey}
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 8,
                          marginBottom: 4,
                          padding: "4px 8px",
                        }}
                      >
                        <span
                          style={{
                            display: "inline-block",
                            width: 8,
                            height: 8,
                            borderRadius: "50%",
                            background: entry.color,
                          }}
                        />
                        <span style={{ color: "#6B7280", fontSize: 14 }}>
                          {entry.name}:{" "}
                          <span style={{ color: "black", fontSize: 13 }}>
                            {entry.value}
                          </span>
                        </span>
                      </div>
                    ))}
                  </div>
                );
              }}
            />
            <Legend
              verticalAlign="top"
              align="right"
              iconType="circle"
              content={renderLegend}
            />
            <Line
              type="monotone"
              dataKey="week1"
              name="Week 1"
              stroke="#22C55E "
              strokeWidth={2}
              dot={{ fill: "#22C55E " }}
              activeDot={{ r: 3 }}
            />
            <Line
              type="monotone"
              dataKey="week2"
              name="Week 2"
              stroke="#FACC15 "
              strokeWidth={2}
              dot={{ fill: "#FACC15 " }}
              activeDot={{ r: 3 }}
            />
            <Line
              type="monotone"
              dataKey="week3"
              name="Week 3"
              stroke="#3B82F6"
              strokeWidth={2}
              dot={{ fill: "#3B82F6" }}
              activeDot={{ r: 3 }}
            />
            <Line
              type="monotone"
              dataKey="week4"
              name="Week 4"
              stroke="#8B5CF6 "
              strokeWidth={2}
              dot={{ fill: "#8B5CF6 " }}
              activeDot={{ r: 3 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ProblemSolvingChart;
