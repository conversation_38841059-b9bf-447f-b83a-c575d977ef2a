import React from "react";

type TeamCompletionProps = {
  profile: string;
  name: string;
  solved: number;
  completion: number;
  available: number;
};

export function TeamCompletion({
  profile,
  name,
  solved,
  completion,
  available,
}: TeamCompletionProps) {
  return (
    <div className="h-fit">
      <div className="w-full flex h-fit items-center ">
        <img
          src={`profile`}
          alt="profile"
          className="w-12 h-12 rounded-full  mb-2"
        />
        <p className="align-middle">{name}</p>
        <div className="flex-1 ml-8">
          <div className="w-full h-3 bg-[#abf0c4] rounded-full overflow-hidden">
            <div
              className="h-full rounded-full transition-all duration-300"
              style={{
                width: `${completion}%`,
                background: "#54D62C",
              }}
            />
          </div>
          <p className=" ">
            {solved} Solved | {completion}% Completion | {available} Available
          </p>
        </div>
      </div>
      <hr className="border border-gray-200  w-full p-0" />
    </div>
  );
}
