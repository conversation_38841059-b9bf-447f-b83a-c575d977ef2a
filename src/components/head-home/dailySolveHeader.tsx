import React from "react";
import { useState } from "react";

const DailySolveHeader = () => {
  const [selectedDate, setSelectedDate] = useState<number>(
    new Date().getMonth()
  );
  const date = [
    "Wed Apr 19 2023",
    "Thu Apr 20 2023",
    "Fri Apr 21 2023",
    "Sat Apr 22 2023",
    "Sun Apr 23 2023",
    "Mon Apr 24 2023",
    "Tue Apr 25 2023",
    "Wed Apr 26 2023",
    "Thu Apr 27 2023",
  ];

  return (
    <div className="flex items-center justify-between mb-4">
      <p className="text-md font-semibold text-wrap">Daily Solve</p>
      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
        <select
          value={selectedDate}
          onChange={(e) => setSelectedDate(Number(e.target.value))}
          className="text-sm font-semibold"
          style={{
            background: "#f3f4f6",
            border: "none",
            borderRadius: 6,
            padding: "6px 12px",
            fontSize: 15,
            fontWeight: 400,
            cursor: "pointer",
          }}
        >
          {date.map((month, idx) => (
            <option key={month} value={idx}>
              {month}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default DailySolveHeader;
