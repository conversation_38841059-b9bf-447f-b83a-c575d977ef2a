"use client";
import React, { useState } from "react";

interface SummaryHeaderProps {
  title: string;
  choices: string[];
}

const SummaryHeader: React.FC<SummaryHeaderProps> = ({ title, choices }) => {
  const [selectedChoice, setSelectedChoice] = useState<number>(0);

  return (
    <div className="flex items-center justify-between mb-4">
      <p className="text-lg font-semibold text-wrap">{title}</p>
      <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
        <select
          value={selectedChoice}
          onChange={(e) => setSelectedChoice(Number(e.target.value))}
          className="text-sm font-semibold "
          style={{
            borderBottom: "2px solid #d1d5db",
            padding: "6px 0px 6px 0px",
            fontSize: 16,
            fontWeight: 400,
            cursor: "pointer",
          }}
        >
          {choices.map((choice, idx) => (
            <option key={choice} value={idx}>
              {choice}
            </option>
          ))}
        </select>
        <img
          src="/refresh.svg"
          alt="refresh"
          className="w-6 h-6 cursor-pointer"
        />
      </div>
    </div>
  );
};

export default SummaryHeader;
