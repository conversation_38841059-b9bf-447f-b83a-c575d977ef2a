import React from "react";
import { Progress } from "./progress";
import <PERSON><PERSON><PERSON><PERSON><PERSON> from "./statePieChart";
import CompletionHeader from "./summaryHeader";
import DetailTeamCompletion from "./detailTeamCompletion";
import SummaryHeader from "./summaryHeader";

const teams = [
  {
    profile: "/profile1.jpg",
    name: "<PERSON>",
    solved: 10,
    completion: 75,
    available: 5,
  },
  {
    profile: "/profile2.jpg",
    name: "<PERSON>",
    solved: 8,
    completion: 60,
    available: 7,
  },
];
const Completion = () => {
  return (
    <div className="bg-white rounded-xl shadow p-6 w-full ">
      <CompletionHeader   title="Exercise Completion Summary"
        choices={["Progress", "Camp I", "Progress II", "Camp II"]}/>
      <div className="flex  items-center ">
        <div className="w-3/5">
          <Progress
            value={75}
            color="#54D62C"
            total={85}
            solved={63}
            available={13}
          />
        </div>
        <div className="w-2/5 flex justify-end">
          <StatePieChart />
        </div>
      </div>
      <DetailTeamCompletion teams={teams} />
    </div>
  );
};

export default Completion;
