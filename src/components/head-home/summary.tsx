"use client";
import React, { useState } from "react";
import Completion from "./completion";
import ExerciseSummary from "./exerciseSummary";
import Division from "./division";
const Summary = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  return (
    <div className="bg-white p-6 w-full  ">
      <div className="flex gap-6">
        <div className="flex items-center mb-6">
          <img src="/summary.svg" alt="Summary" className="w-10 h-10 mr-4" />
          <span className="font-bold text-xl">Summary</span>
        </div>
        <div className="flex space-x-6 mb-4">
          {[
            "Attendance",
            "Consistency",
            "Completion",
            "Exercise",
            "Upsolving",
            "Divisions",
          ].map((tab, idx) => (
            <button
              key={tab}
              className={`py-2 px-4 text-sm font-semibold ${
                selectedTab === idx
                  ? "border-b-2  border-green-500"
                  : "text-gray-500"
              }`}
              onClick={() => setSelectedTab(idx)}
              type="button"
            >
              {tab}
            </button>
          ))}
        </div>{" "}
      </div>
      <div>
        {selectedTab === 0 && (
          <div>
            <p>Attendance summary content.</p>
          </div>
        )}
        {selectedTab === 1 && <div></div>}
        {selectedTab === 2 && (
          <div>
            {/* Consistency content goes here */}
            <Completion />
          </div>
        )}
        {selectedTab === 3 && (
          <div>
            <ExerciseSummary />
          </div>
        )}
        {selectedTab === 5 && (
          <div>
            <Division />
          </div>
        )}
      </div>
    </div>
  );
};

export default Summary;
