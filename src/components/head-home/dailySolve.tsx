"use client";
import * as React from "react";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  Label,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import DailySolveHeader from "./dailySolveHeader";

const chartData = [
  { label: "Solved", value: 1, color: "#22C55E" },
  { label: "Missed", value: 10, color: "#EF4444" },
];

export default function DailySolve() {
  const total = React.useMemo(
    () => chartData.reduce((acc, curr) => acc + curr.value, 0),
    []
  );
  const solved = chartData.find((d) => d.label === "Solved")?.value ?? 0;
  return (
    <div className="bg-white rounded-xl shadow p-6 w-full flex flex-col md:flex-1/3">
      <div className="mb-2">
        <DailySolveHeader />
        <div className="flex w-full items-end justify-end">
          <img src="/tick.svg" className="w-4 h-4 object-contain" />
        </div>
      </div>
      <div className="flex-1 flex flex-col items-center justify-center">
        <ResponsiveContainer width={250} height={250}>
          <PieChart>
            <Pie
              data={chartData}
              dataKey="value"
              nameKey="label"
              cx="50%"
              cy="50%"
              innerRadius={90}
              outerRadius={100}
              paddingAngle={2}
              stroke="#fff"
              startAngle={90}
              endAngle={-270}
              labelLine={false}
              label={({ percent, cx, cy, midAngle, outerRadius, index }) => {
                if (percent < 0.05) return null;
                const RADIAN = Math.PI / 180;
                const radius = outerRadius + 2;
                const x = cx + radius * Math.cos(-midAngle * RADIAN);
                const y = cy + radius * Math.sin(-midAngle * RADIAN);
                return (
                  <text
                    x={x}
                    y={y}
                    fill="#222"
                    textAnchor={x > cx ? "start" : "end"}
                    dominantBaseline="central"
                    fontSize={12}
                    fontWeight={600}
                  >
                    {(percent * 100).toFixed(0)}%
                  </text>
                );
              }}
            >
              {chartData.map((entry) => (
                <Cell key={entry.label} fill={entry.color} />
              ))}
              <Label
                position="center"
                content={({ viewBox }) => {
                  if (!viewBox) return null;
                  const { cx, cy } = viewBox;
                  return (
                    <>
                      <text
                        x={cx}
                        y={cy + 22}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="text-xs fill-gray-500"
                      >
                        Solved
                      </text>
                      <text
                        x={cx}
                        y={cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="text-2xl font-bold "
                      >
                        {solved}/{total}
                      </text>
                    </>
                  );
                }}
              />
            </Pie>
            <Tooltip
              formatter={(value: number, name: string) => [`${value}`, name]}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
      <div className="mb-8 flex items-center justify-center">
        <img
          src="/profile.jpg"
          alt="Profile"
          className="h-8 w-8 rounded-full object-cover"
        />
      </div>
      <hr className="border-gray-200 mb-4" />
      <div className="flex gap-4 ml-6 justify-center">
        {chartData.map((entry) => (
          <div key={entry.label} className="flex items-center gap-1">
            <span
              className="inline-block w-3 h-3 rounded-full"
              style={{ backgroundColor: entry.color }}
            ></span>
            <span className="text-xs text-gray-700">{entry.label}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
