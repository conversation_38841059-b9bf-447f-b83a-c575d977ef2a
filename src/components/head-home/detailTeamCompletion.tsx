"use client";
import * as React from "react";
import { Team } from "@/types/teamcompletion";
import { TeamCompletion } from "./teamCompletion";

type DetailTeamCompletionProps = {
  teams: Team[];
};
const DetailTeamCompletion = ({ teams }: DetailTeamCompletionProps) => {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <div className="w-full  rounded-lg bg-white cursor-pointer mt-8">
      {!isOpen && <hr className="border border-gray-100 w-full p-0 mt-4" />}
      <button
        className="flex w-full items-center justify-between cursor-pointer px-4 py-3 text-left text-base  focus:outline-none"
        onClick={() => setIsOpen((open) => !open)}
      >
        <span className="">Detail Team Completion</span>
        {isOpen ? (
          <img
            src="/right-arrow.svg"
            className="w-5 h-5 text-gray-500 -rotate-90"
          />
        ) : (
          <img
            src="/right-arrow.svg"
            className="w-5 h-5 text-gray-500 rotate-90"
          />
        )}
      </button>
      {isOpen && (
        <div className="px-6 flex flex-col  ">
          {teams.map((team, idx) => (
            <TeamCompletion key={idx} {...team} />
          ))}
        </div>
      )}
    </div>
  );
};

export default DetailTeamCompletion;
