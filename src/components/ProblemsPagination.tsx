import { ChevronDown, ChevronUp } from 'lucide-react';

export const ProblemsPagination = () => {
  return (
    <div className="flex items-center justify-end gap-4 px-6 py-4 bg-white border-t border-gray-100">
      <div className="flex items-center gap-2 text-sm text-gray-600">
        <span>Rows per page:</span>
        <select className="border border-gray-300 rounded px-2 py-1 text-sm focus:ring-2 focus:ring-green-500 focus:border-transparent">
          <option value="10">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
          <option value="100" defaultValue="100">100</option>
        </select>
      </div>
      
      <div className="text-sm text-gray-600">
        1-100 of 1604
      </div>
      
      <div className="flex items-center gap-1">
        <button className="p-1 rounded hover:bg-gray-100 text-gray-400 hover:text-gray-600 transition-colors">
          <ChevronDown className="w-4 h-4 rotate-90" />
        </button>
        <button className="p-1 rounded hover:bg-gray-100 text-gray-600 hover:text-gray-800 transition-colors">
          <ChevronUp className="w-4 h-4 rotate-90" />
        </button>
      </div>
    </div>
  );
};