import { ExternalLink } from 'lucide-react';
import  ChevronDownIcon   from "../../public/chovrionDownButton.svg";
import ChevronUpButton from "../../public/chovrionupButton.svg";



interface Problem {
  id: number;
  name: string;
  difficulty: string;
  tags: string[];
  solved: boolean;
  addedDays: number;
  votes: number;
  userVote: number;
  link: string;
}

interface ProblemsTableProps {
  problems: Problem[];
  visibleColumns: Record<string, boolean>;
  handleVote: (problemId: number, voteType: number) => void;
}

export const ProblemsTable = ({ problems, visibleColumns, handleVote }: ProblemsTableProps) => {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'text-green-600 bg-green-50 px-2 py-1 rounded text-xs font-medium';
      case 'Medium': return 'text-yellow-600 bg-yellow-50 px-2 py-1 rounded text-xs font-medium';
      case 'Hard': return 'text-red-600 bg-red-50 px-2 py-1 rounded text-xs font-medium';
      default: return 'text-gray-600 bg-gray-50 px-2 py-1 rounded text-xs font-medium';
    }
  };

  return (
    <div className="overflow-x-auto">
      {/* Table Header */}
      <div className="flex items-center px-6 py-4 bg-white border-b border-gray-200 text-sm font-bold text-gray-700">
        {visibleColumns.difficulty && <div className="w-20 text-left ">Diffi...</div>}
        {visibleColumns.name && <div className="flex-1  px-2 border-l-2 border-l-gray-400">Name</div>}
        {visibleColumns.tag && <div className="w-32 text-center border-l-2 border-l-gray-400">Tag</div>}
        {visibleColumns.solved && <div className="w-16 text-center border-l-2 border-l-gray-400">Solved</div>}
        {visibleColumns.added && <div className="w-16 text-center border-l-2 border-l-gray-400">Added</div>}
        {visibleColumns.vote && <div className="w-16 text-center border-l-2 border-l-gray-400">Vote</div>}
        {visibleColumns.link && <div className="w-16 text-center border-l-2 border-l-gray-400">Link</div>}
      </div>

      {/* Table Body */}
      <div className="bg-white">
        {problems.map((problem) => (
          <div key={problem.id} className="flex items-center px-6 py-4 border-b border-gray-100 hover:bg-gray-50 hover:cursor-pointer transition-colors">
            {visibleColumns.difficulty && (
              <div className="w-20">
                <span className={getDifficultyColor(problem.difficulty)}>
                  {problem.difficulty}
                </span>
              </div>
            )}
            {visibleColumns.name && (
              <div className="flex-1 ">
                <span className="text-gray-900 font-medium hover:text-blue-600 cursor-pointer">
                  {problem.name}
                </span>
              </div>
            )}
            {visibleColumns.tag && (
              <div className="w-32 text-center">
                <span className="text-sm text-gray-600">
                  {problem.tags.join(', ').substring(0, 15)}...
                </span>
              </div>
            )}
            {visibleColumns.solved && (
              <div className="w-16 text-center">
                <span className="text-gray-400">-</span>
              </div>
            )}
            {visibleColumns.added && (
              <div className="w-16 text-center">
                <span className="text-gray-600">{problem.addedDays}d</span>
              </div>
            )}
            {visibleColumns.vote && (
              <div className="w-16 text-center">
                <div className="flex items-center justify-end gap-1">
                  <button 
                    onClick={() => handleVote(problem.id, 1)}
                    className={`p-1 rounded-full transition-colors ${
                      problem.userVote === 1 
                        ? 'text-green-600 bg-green-50' 
                        : 'text-gray-400  hover:bg-green-50'
                    }`}
                  >
                    <ChevronUpButton className="w-3 h-3" />
                  
                  </button>
                  <span className={`text-sm font-medium  text-center ${
                    problem.votes > 0 ? 'text-green-600' : 
                    problem.votes < 0 ? 'text-red-600' : 'text-gray-500'
                  }`}>
                    {problem.votes}
                  </span>
                  <button 
                    onClick={() => handleVote(problem.id, -1)}
                    className={`p-1 rounded-full transition-colors ${
                      problem.userVote === -1 
                        ? 'text-red-600 bg-red-50' 
                        : 'text-gray-400  hover:bg-red-50'
                    }`}
                  >
               
                        <ChevronDownIcon  className="w-3 h-3 " /> 

                  </button>
                </div>
              </div>
            )}
            {visibleColumns.link && (
              <div className="w-16 text-right">
                <button className="text-blue-500 hover:text-blue-600 transition-colors">
                  <ExternalLink className="w-4 h-4" />
                </button>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};