import { createSlice, PayloadAction } from "@reduxjs/toolkit";

import { DailyProblemsState, Problem } from "@/types/dailyProblemsTypes";

const initialState: DailyProblemsState = {
  dailyProblems: [],
  filteredProblems: [],
  loading: false,
  error: "",
  searchTerm: "",
  selectedSuperGroup: "",
  selectedProblems: [],
  allProblems: [],
};

export const dailyProblemsSlice = createSlice({
  name: "dailyProblems",
  initialState,
  reducers: {
    // Action to set the search term and filter problems
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
      state.filteredProblems = filterProblems(state);
    },
    setSelectedSuperGroup: (state, action: PayloadAction<string>) => {
      state.selectedSuperGroup = action.payload;
      state.filteredProblems = filterProblems(state);
    },
    setSelectedProblems: (state, action: PayloadAction<Problem[]>) => {
      state.dailyProblems = action.payload;

      state.filteredProblems = filterProblems(state);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    // setSelectedProblems: (state, action: PayloadAction<Problem[]>) => {
    //   state.selectedProblems = action.payload;
    //   state.dailyProblems = action.payload; // If dailyProblems should mirror selectedProblems
    // },

    setAllProblems: (state, action: PayloadAction<Problem[]>) => {
      state.allProblems = action.payload;
      state.filteredProblems = filterProblems(state);
    },
    setFilteredProblems: (state, action: PayloadAction<Problem[]>) => {
      state.filteredProblems = action.payload;
    },
  },
});

// Helper function to filter problems based on search term
const filterProblems = (state: DailyProblemsState): Problem[] => {
  let filtered = state.dailyProblems;

  // Filter by search term
  if (state.searchTerm.trim() !== "") {
    filtered = filtered.filter(
      (dailyProblems) =>
        dailyProblems.title
          .toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        dailyProblems.source
          .toLowerCase()
          .includes(state.searchTerm.toLowerCase()) ||
        dailyProblems.difficulty
          .toLowerCase()
          .includes(state.searchTerm.toLowerCase())
    );
  }

  return filtered;
};

// Initialize filtered users
initialState.filteredProblems = filterProblems(initialState);

export const {
  setAllProblems,
  setFilteredProblems,
  setSearchTerm,
  setSelectedSuperGroup,
  setSelectedProblems,
  setLoading,
  setError,
} = dailyProblemsSlice.actions;

export default dailyProblemsSlice.reducer;
