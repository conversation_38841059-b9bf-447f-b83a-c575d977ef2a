// src/store/slices/usersSlice.ts

import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { User, UsersState } from "@/types/index";

const initialState: UsersState = {
  users: [],
  filteredUsers: [],
  loading: false,
  error: null,
  searchTerm: "",
  selectedGroup: "All",
  viewMode: "card",
};

const usersSlice = createSlice({
  name: "users",
  initialState,
  reducers: {
    setSearchTerm: (state, action: PayloadAction<string>) => {
      state.searchTerm = action.payload;
      state.filteredUsers = filterUsers(state);
    },
    setSelectedGroup: (state, action: PayloadAction<string>) => {
      state.selectedGroup = action.payload;
      state.filteredUsers = filterUsers(state);
    },
    setViewMode: (state, action: PayloadAction<"card" | "list">) => {
      state.viewMode = action.payload;
    },
    setUsers: (state, action: PayloadAction<User[]>) => {
      state.users = action.payload;
      state.filteredUsers = filterUsers(state);
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

// Helper function to filter users based on search term and selected group
const filterUsers = (state: UsersState): User[] => {
  let filtered = state.users;

  // Filter by search term
  if (state.searchTerm) {
    filtered = filtered.filter(
      (user) =>
        user.name.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
        user.title.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
        user.group.toLowerCase().includes(state.searchTerm.toLowerCase())
    );
  }

  // Filter by group
  if (state.selectedGroup && state.selectedGroup !== "All") {
    filtered = filtered.filter((user) => user.group === state.selectedGroup);
  }

  return filtered;
};

// Initialize filtered users
initialState.filteredUsers = filterUsers(initialState);

export const {
  setSearchTerm,
  setSelectedGroup,
  setViewMode,
  setUsers,
  setLoading,
  setError,
} = usersSlice.actions;

export default usersSlice.reducer;
