import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import {
  AttendanceState,
  AttendanceStatus,
  SessionType,
  User,
  AttendanceRecord,
  AttendanceSession,
  Sessions,
  SubmitAttendanceRequest,
  SubmitAttendanceResponse,
} from "@/types/index";

const sampleSessions: Sessions[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(
  (sessionNumber) => ({ session: sessionNumber })
);

const initialState: AttendanceState = {
  currentSession: {
    sessionNumber: null,
    sessionType: null,
    group: null,
  },
  sessionUsers: {},
  attendanceSessions: [],
  availableSessions: sampleSessions,
  isLoading: false,
  isSubmitting: false,
  error: null,
  validationErrors: {
    session: null,
    sessionType: null,
  },
};

// Async thunks for API calls
export const submitAttendance = createAsyncThunk(
  "attendance/submitAttendance",
  async (attendanceData: SubmitAttendanceRequest, { rejectWithValue }) => {
    try {
      // Replace this with dummy data to represent getting the data from the API
      const response = await fetch("/api/attendance/submit", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(attendanceData),
      });

      if (!response.ok) {
        throw new Error("Failed to submit attendance");
      }

      const result: SubmitAttendanceResponse = await response.json();
      return result;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }
);

export const fetchAvailableSessions = createAsyncThunk(
  "attendance/fetchAvailableSessions",
  async (_, { rejectWithValue }) => {
    try {
      // Replace this with your actual API call
      const response = await fetch("/api/attendance/sessions");

      if (!response.ok) {
        throw new Error("Failed to fetch sessions");
      }

      const sessions: Sessions[] = await response.json();
      return sessions;
    } catch (error) {
      return rejectWithValue(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }
);
const attendanceSlice = createSlice({
  name: "attendance",
  initialState,
  reducers: {
    // Set current session details
    setCurrentSession: (
      state,
      action: PayloadAction<{
        sessionNumber: number;
        sessionType: SessionType;
        group: string;
      }>
    ) => {
      state.currentSession = action.payload;
      state.validationErrors.session = null;
      state.validationErrors.sessionType = null;
      console.log("Set current session:", action.payload);
    },

    // Initialize users for attendance session
    initializeSessionUsers: (state, action: PayloadAction<User[]>) => {
      const users = action.payload;
      state.sessionUsers = {};

      users.forEach((user) => {
        state.sessionUsers[user.id] = {
          user,
          status: null, // Start with no status
        };
      });

      console.log(
        "Initialized session users with no status:",
        state.sessionUsers
      );
    },

    // Update individual user attendance status
    updateUserAttendanceStatus: (
      state,
      action: PayloadAction<{
        userId: string;
        status: AttendanceStatus;
      }>
    ) => {
      const { userId, status } = action.payload;
      console.log("Updating status for user:", userId, "to:", status);

      if (state.sessionUsers[userId]) {
        state.sessionUsers[userId].status = status;
        console.log("Status updated successfully");
      } else {
        console.error("Failed to update status: User not found in session");
      }
    },

    setAllUsersPresent: (state) => {
      console.log("Marking all users as present");
      Object.keys(state.sessionUsers).forEach((userId) => {
        state.sessionUsers[userId].status = "PRESENT";
      });
      console.log("All users marked as present");
    },

    // Set session number
    setSessionNumber: (state, action: PayloadAction<number>) => {
      state.currentSession.sessionNumber = action.payload;
      state.validationErrors.session = null;
    },

    // Set session type
    setSessionType: (state, action: PayloadAction<SessionType>) => {
      state.currentSession.sessionType = action.payload;
      state.validationErrors.sessionType = null;
    },

    // Clear current session
    clearCurrentSession: (state) => {
      state.currentSession = {
        sessionNumber: null,
        sessionType: null,
        group: null,
      };
      state.sessionUsers = {};
      state.validationErrors = {
        session: null,
        sessionType: null,
      };
    },

    // Set validation errors
    setValidationErrors: (
      state,
      action: PayloadAction<{
        session?: string | null;
        sessionType?: string | null;
      }>
    ) => {
      if (action.payload.session !== undefined) {
        state.validationErrors.session = action.payload.session;
      }
      if (action.payload.sessionType !== undefined) {
        state.validationErrors.sessionType = action.payload.sessionType;
      }
    },

    // Clear errors
    clearError: (state) => {
      state.error = null;
    },

    // Reset validation errors
    resetValidationErrors: (state) => {
      state.validationErrors = {
        session: null,
        sessionType: null,
      };
    },

    // Add completed attendance session to history
    addAttendanceSession: (state, action: PayloadAction<AttendanceSession>) => {
      state.attendanceSessions.push(action.payload);
    },
  },

  extraReducers: (builder) => {
    // Submit attendance
    builder
      .addCase(submitAttendance.pending, (state) => {
        state.isSubmitting = true;
        state.error = null;
      })
      .addCase(submitAttendance.fulfilled, (state, action) => {
        state.isSubmitting = false;

        // Create attendance records
        const attendanceRecords: AttendanceRecord[] = Object.values(
          state.sessionUsers
        ).map(({ user, status }) => ({
          id: `${user.id}-${state.currentSession.sessionNumber}-${state.currentSession.sessionType}`,
          userId: user.id,
          userName: user.name,
          userAvatar: user.avatar,
          sessionNumber: state.currentSession.sessionNumber!,
          sessionType: state.currentSession.sessionType!,
          status,
          timestamp: new Date().toISOString(),
          group: state.currentSession.group!,
        }));

        // Add to attendance sessions history
        const newSession: AttendanceSession = {
          sessionNumber: state.currentSession.sessionNumber!,
          sessionType: state.currentSession.sessionType!,
          attendanceRecords,
          isSubmitted: true,
          submittedAt: new Date().toISOString(),
        };

        state.attendanceSessions.push(newSession);

        // Clear current session after successful submission
        state.currentSession = {
          sessionNumber: null,
          sessionType: null,
          group: null,
        };
        state.sessionUsers = {};
      })
      .addCase(submitAttendance.rejected, (state, action) => {
        state.isSubmitting = false;
        state.error = action.payload as string;
      });

    // Fetch available sessions
    builder
      .addCase(fetchAvailableSessions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAvailableSessions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.availableSessions = action.payload;
      })
      .addCase(fetchAvailableSessions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  setCurrentSession,
  initializeSessionUsers,
  updateUserAttendanceStatus,
  setAllUsersPresent,
  setSessionNumber,
  setSessionType,
  clearCurrentSession,
  setValidationErrors,
  clearError,
  resetValidationErrors,
  addAttendanceSession,
} = attendanceSlice.actions;

// Export reducer
export default attendanceSlice.reducer;
