// src/utils/constants.ts
import type { StaticImageData } from "next/image";
import codeforcesicon from "../../public/icons/codeforcesicon.svg";
import hackerrankicon from "../../public/icons/hackerrankicon.svg";
import telegramicon from "../../public/icons/telegramicon.svg";
import instagramicon from "../../public/icons/instagramicon.svg";
export const BADGE_ICONS = {
  codeforces: codeforcesicon,
  hackerrank: hackerrankicon,
  telegram: telegramicon,
  instagram: instagramicon,
  default: "⭐",
} as const;

export const GROUP_OPTIONS = [
  { value: "All", label: "All Groups" },
  { value: "G69", label: "Group G69" },
  { value: "G52", label: "Group G52" },
  { value: "G42", label: "Group G42" },
] as const;

export const VIEW_MODES = {
  CARD: "card",
  LIST: "list",
} as const;

export const TABS = {
  USERS: "users",
  GROUPS: "groups",
  COUNTRIES: "countries",
} as const;

import avatarImage from "@/utils/avatar.jpg";
// Mock data for development
export const MOCK_USERS = [
  {
    id: "1",
    name: "Tamirat kebede",
    title: "Head",
    group: "G69",
    avatar: "/avatars/tamirat.jpg",
    problems: 414,
    submissions: 462,
    dedicatedTime: "10.97k",
    rating: 1400,
    badges: ["codeforces", "hackerrank", "telegram", "instagram"],
    imageString: avatarImage,
  },
  {
    id: "2",
    name: "Samuel Endale Gebrekirstose",
    title: "Student",
    group: "G52",
    avatar: "/avatars/samuel.jpg",
    problems: 361,
    submissions: 369,
    dedicatedTime: "9.55k",
    rating: 1400,
    badges: ["codeforces", "hackerrank", "telegram"],
    imageString: avatarImage,
  },
  {
    id: "3",
    name: "Yohannes Desta Gebru",
    title: "Student",
    group: "G42",
    avatar: "/avatars/yohannes.jpg",
    problems: 37,
    submissions: 37,
    dedicatedTime: "916",
    rating: 1400,
    badges: ["codeforces", "hackerrank", "telegram"],
    // imageString: "/src/utils/avatar.jpg",
  },
  {
    id: "4",
    name: "Kaleb Asratemedhin Bekele",
    title: "Student",
    group: "G69",
    avatar: "/avatars/kaleb.jpg",
    problems: 488,
    submissions: 462,
    dedicatedTime: "10,794",
    rating: 1400,
    badges: ["codeforces", "hackerrank", "telegram", "instagram"],
    // imageString: "/src/utils/avatar.jpg",
  },
  {
    id: "5",
    name: "Nathan Giithinji Rugo",
    title: "Student",
    group: "G52",
    avatar: "/avatars/nathan.jpg",
    problems: 453,
    submissions: 462,
    dedicatedTime: "19,680",
    rating: 1400,
    badges: ["codeforces", "hackerrank", "telegram"],
    // imageString: "/src/utils/avatar.jpg",
  },
];

// Utility functions
export const formatNumber = (num: number): string => {
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + "k";
  }
  return num.toString();
};

export const getBadgeIcon = (badge: string): StaticImageData | string => {
  return BADGE_ICONS[badge as keyof typeof BADGE_ICONS] || BADGE_ICONS.default;
};
export const getInitials = (name: string): string => {
  return name
    .split(" ")
    .map((word) => word.charAt(0))
    .join("")
    .toUpperCase()
    .slice(0, 2);
};
