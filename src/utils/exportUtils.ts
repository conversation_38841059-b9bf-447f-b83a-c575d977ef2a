interface Problem {
    id: number;
    name: string;
    difficulty: string;
    tags: string[];
    solved: boolean;
    addedDays: number;
    votes: number;
    userVote: number;
    link: string;
  }
  
  export const exportToCSV = (problems: Problem[], visibleColumns: Record<string, boolean>) => {
    // Get visible column headers
    const columnMapping = {
      id: 'ID',
      difficulty: 'Difficulty',
      name: 'Name',
      tag: 'Tags',
      track: 'Track',
      contest: 'Contest',
      solved: 'Solved',
      added: 'Added (days)',
      vote: 'Votes',
      link: 'Link'
    };
  
    const visibleColumnKeys = Object.keys(visibleColumns).filter(key => visibleColumns[key]);
    const headers = visibleColumnKeys.map(key => columnMapping[key as keyof typeof columnMapping]);
  
    // Convert problems to CSV rows
    const csvRows = problems.map(problem => {
      const row: string[] = [];
      
      visibleColumnKeys.forEach(columnKey => {
        switch (columnKey) {
          case 'id':
            row.push(problem.id.toString());
            break;
          case 'difficulty':
            row.push(problem.difficulty);
            break;
          case 'name':
            row.push(`"${problem.name}"`);
            break;
          case 'tag':
            row.push(`"${problem.tags.join(', ')}"`);
            break;
          case 'track':
            row.push('');
            break;
          case 'contest':
            row.push('');
            break;
          case 'solved':
            row.push(problem.solved ? 'Yes' : 'No');
            break;
          case 'added':
            row.push(`${problem.addedDays}d`);
            break;
          case 'vote':
            row.push(problem.votes.toString());
            break;
          case 'link':
            row.push(problem.link);
            break;
          default:
            row.push('');
        }
      });
      
      return row.join(',');
    });
  
    // Create CSV content
    const csvContent = [headers.join(','), ...csvRows].join('\n');
    
    // Download CSV file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `problems_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };
  
  export const printTable = (problems: Problem[], visibleColumns: Record<string, boolean>) => {
    const columnMapping = {
      id: 'ID',
      difficulty: 'Difficulty',
      name: 'Name',
      tag: 'Tags',
      track: 'Track',
      contest: 'Contest',
      solved: 'Solved',
      added: 'Added (days)',
      vote: 'Votes',
      link: 'Link'
    };
  
    const visibleColumnKeys = Object.keys(visibleColumns).filter(key => visibleColumns[key]);
    const headers = visibleColumnKeys.map(key => columnMapping[key as keyof typeof columnMapping]);
  
    // Create HTML table for printing
    let tableHTML = `
      <html>
        <head>
          <title>Problems List</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            h1 { color: #333; margin-bottom: 20px; }
            table { border-collapse: collapse; width: 100%; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f5f5f5; font-weight: bold; }
            tr:nth-child(even) { background-color: #f9f9f9; }
            .difficulty-easy { color: #16a34a; background-color: #f0fdf4; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
            .difficulty-medium { color: #ca8a04; background-color: #fefce8; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
            .difficulty-hard { color: #dc2626; background-color: #fef2f2; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
            @media print {
              body { margin: 0; }
              table { font-size: 12px; }
            }
          </style>
        </head>
        <body>
          <h1>Problems List</h1>
          <p>Generated on ${new Date().toLocaleDateString()}</p>
          <table>
            <thead>
              <tr>
                ${headers.map(header => `<th>${header}</th>`).join('')}
              </tr>
            </thead>
            <tbody>
    `;
  
    problems.forEach(problem => {
      tableHTML += '<tr>';
      
      visibleColumnKeys.forEach(columnKey => {
        switch (columnKey) {
          case 'id':
            tableHTML += `<td>${problem.id}</td>`;
            break;
          case 'difficulty':
            const difficultyClass = `difficulty-${problem.difficulty.toLowerCase()}`;
            tableHTML += `<td><span class="${difficultyClass}">${problem.difficulty}</span></td>`;
            break;
          case 'name':
            tableHTML += `<td>${problem.name}</td>`;
            break;
          case 'tag':
            tableHTML += `<td>${problem.tags.join(', ')}</td>`;
            break;
          case 'track':
            tableHTML += '<td>-</td>';
            break;
          case 'contest':
            tableHTML += '<td>-</td>';
            break;
          case 'solved':
            tableHTML += `<td>${problem.solved ? 'Yes' : 'No'}</td>`;
            break;
          case 'added':
            tableHTML += `<td>${problem.addedDays}d</td>`;
            break;
          case 'vote':
            tableHTML += `<td>${problem.votes}</td>`;
            break;
          case 'link':
            tableHTML += `<td>${problem.link}</td>`;
            break;
          default:
            tableHTML += '<td>-</td>';
        }
      });
      
      tableHTML += '</tr>';
    });
  
    tableHTML += `
            </tbody>
          </table>
        </body>
      </html>
    `;
  
   
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(tableHTML);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  };