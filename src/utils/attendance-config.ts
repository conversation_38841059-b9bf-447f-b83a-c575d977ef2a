import {  Clock, X, AlertCircle, Check } from 'lucide-react';
import { LucideIcon } from 'lucide-react';
// Status configuration
interface StatusConfigItem {
  icon: LucideIcon;
  color: string;
  bgColor: string;
  activeBg: string;
}

type StatusType = 'LATE' | 'ABSENT' | 'EXCUSED' | 'PRESENT';

type StatusConfig = {
  [key in StatusType]: StatusConfigItem;
};

const statusConfig: StatusConfig = {
  LATE: {
    icon: Clock,
    color: 'text-blue-500',
    bgColor: ' hover:bg-blue-100',
    activeBg: 'text-blue-500 hover:bg-blue-100'
  },
  ABSENT: {
    icon: X,
    color: 'text-red-500',
    bgColor: ' hover:bg-red-100',
    activeBg: 'text-red-500 hover:bg-red-100'
  },
  EXCUSED: {
    icon: AlertCircle,
    color: 'text-yellow-500',
    bgColor: ' hover:bg-yellow-100',
    activeBg: 'text-yellow-500 hover:bg-yellow-100'
  },
  PRESENT: {
    icon: Check,
    color: 'text-green-500',
    bgColor: 'hover:bg-green-100',
    activeBg: 'text-green-500 hover:bg-green-100'
  }
};

export {statusConfig}