
import DropDownMenu from "@/components/ForoumComponents/DropDownMenu"
import ForoumPostCard from "@/components/ForoumComponents/ForoumPostCard"

export default function foroum() {
  return (
    <div className="min-h-screen bg-white text-white  mt-[0px]">
      {/* Header */}
      <div className="flex items-center justify-between p-6  border-slate-700">

        <div>
          <h1 className="text-3xl font-bold text-black">Forum</h1>
          <p className=" text-lg text-slate-400 mt-4">Discussion forum</p>
        </div>

        <div className=" min-h-12 min-w-[150px] flex items-center bg-green-600 hover:bg-green-700 text-white rounded-lg px-6 py-2  font-semibold cursor-pointer transition-colors shadow-[0px_10px_20px_0_rgba(34,197,94,0.4)]">
          <img src="/plusicon.svg" alt="plus icon" className="h-6 w-6 mr-2" />
          New Post
        </div>
      </div>

      {/* Filters */}
    <DropDownMenu />

      {/* Forum Posts */}
    <ForoumPostCard />
    </div>
  )
}
