"use client";

import { useMemo } from "react";
import TrackCard from "@/components/tracks/TrackCard";
import ProgressCircle from "@/components/ui/ProgressCircle";
import { Track } from "@/types";

export default function TracksPage() {
  // Mock data
  const tracks: Track[] = [
    {
      id: "progress-ii",
      title: "Progress II",
      type: "Problems",
      solved: 100,
      available: 27,
      vote: 14,
    },
    {
      id: "progress",
      title: "Progress",
      type: "Problems",
      solved: 67,
      available: 15,
      vote: 25,
    },
    {
      id: "camp-i",
      title: "Camp I",
      type: "Problems",
      solved: 56,
      available: 13,
      vote: 18,
    },
    {
      id: "python",
      title: "Python",
      type: "Problems",
      solved: 34,
      available: 13,
      vote: 16,
    },
    {
      id: "camp-ii",
      title: "Camp II",
      type: "Problems",
      solved: 8,
      available: 2,
      vote: 0,
    },
    {
      id: "atcoder-dp",
      title: "AtCoder: Educational DP Tasks",
      type: "Problems",
      solved: 0,
      available: 0,
      vote: 1,
    },
  ];

  // Memoize calculations to prevent unnecessary recalculations on re-renders
  const { totalSolved, totalAvailable, totalProblems, overallProgress } =
    useMemo(() => {
      const solved = tracks.reduce((sum, track) => sum + track.solved, 0);
      const available = tracks.reduce((sum, track) => sum + track.available, 0);
      const total = solved + available;
      const progress = total > 0 ? Math.round((solved / total) * 100) : 0;

      return {
        totalSolved: solved,
        totalAvailable: available,
        totalProblems: total,
        overallProgress: progress,
      };
    }, [tracks]);

  return (
    <main className="p-6 space-y-8">
      {/* Tracks Grid */}
      <section
        aria-label="Track Cards"
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {tracks.map((track) => (
          <TrackCard key={track.id} {...track} />
        ))}
      </section>

      {/* Progress Summary Section */}
      <section
        aria-label="Overall Progress"
        className="bg-white rounded-2xl shadow-lg shadow-[#F7F8F9] border border-gray-100 p-6"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 place-items-center relative">
          {/* Dashed line in the middle - only visible on md screens and up */}
          <div className="hidden md:block absolute h-full w-px border border-dashed border-gray-200 left-1/2 transform -translate-x-1/2"></div>

          <div className="flex items-center gap-6 py-4">
            <div className="relative">
              <ProgressCircle
                total={100}
                solved={overallProgress}
                type="percent"
                size="md"
                color="#00ab55"
                className="md:transform md:scale-110"
              />
            </div>
            <div>
              <div className="text-3xl font-bold">{totalSolved}</div>
              <div className="text-sm text-gray-500">Solved</div>
            </div>
          </div>

          <div className="flex items-center gap-6 py-4">
            <div className="relative">
              <ProgressCircle
                total={100}
                solved={100 - overallProgress}
                type="percent"
                size="md"
                color="#f59e0b"
                className="md:transform md:scale-110"
              />
            </div>
            <div>
              <div className="text-3xl font-bold">{totalAvailable}</div>
              <div className="text-sm text-gray-500">Available</div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
}
