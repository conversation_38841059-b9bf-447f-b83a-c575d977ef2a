'use client';

import React, { useState } from 'react';
import AddProblem from '@/components/add-problem/AddProblem';

interface ProblemFormData {
  title: string;
  tags: string[];
  tagInput: string;
  platform: string;
  difficulty: string;
  track: string;
  contest: string;
  url: string;
}

const initialFormData: ProblemFormData = {
  title: '',
  tags: [],
  tagInput: '',
  platform: '',
  difficulty: '',
  track: '',
  contest: '',
  url: '',
};

export default function AddProblemsPage() {
  const [formData, setFormData] = useState<ProblemFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [touched, setTouched] = useState<{ [key: string]: boolean }>({});
  const [deleteProblem, setDeleteProblem] = useState('');
  const [success, setSuccess] = useState(false);

  // Validation
  const errors = {
    title: !formData.title && touched.title,
    platform: !formData.platform && touched.platform,
    difficulty: !formData.difficulty && touched.difficulty,
    url: !formData.url && touched.url,
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement>) => {
    setTouched(prev => ({ ...prev, [e.target.name]: true }));
  };

  // Tag input handlers
  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, tagInput: e.target.value }));
  };
  const handleAddTag = (e?: React.FormEvent) => {
    if (e) e.preventDefault();
    const tag = formData.tagInput.trim();
    if (tag && !formData.tags.includes(tag)) {
      setFormData(prev => ({ ...prev, tags: [...prev.tags, tag], tagInput: '' }));
    }
  };
  const handleRemoveTag = (tag: string) => {
    setFormData(prev => ({ ...prev, tags: prev.tags.filter(t => t !== tag) }));
  };
  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddTag();
    }
    if (e.key === 'Backspace' && !formData.tagInput && formData.tags.length) {
      handleRemoveTag(formData.tags[formData.tags.length - 1]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setTouched({ title: true, platform: true, difficulty: true, url: true });
    if (!formData.title || !formData.platform || !formData.difficulty || !formData.url) return;
    setIsSubmitting(true);
    setSuccess(false);
    // Submit logic here
    setTimeout(() => {
      setIsSubmitting(false);
      setFormData(initialFormData);
      setTouched({});
      setSuccess(true);
      setTimeout(() => setSuccess(false), 2000);
    }, 1000);
  };

  return (
    <div>
      <AddProblem
        formData={formData}
        errors={errors}
        isSubmitting={isSubmitting}
        touched={touched}
        success={success}
        deleteProblem={deleteProblem}
        setDeleteProblem={setDeleteProblem}
        handleChange={handleChange}
        handleBlur={handleBlur}
        handleTagInputChange={handleTagInputChange}
        handleAddTag={handleAddTag}
        handleRemoveTag={handleRemoveTag}
        handleTagInputKeyDown={handleTagInputKeyDown}
        handleSubmit={handleSubmit}
      />
    </div>
  );
}
