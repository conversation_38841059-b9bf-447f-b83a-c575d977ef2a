// src/app/users/page.tsx

"use client";

import React, { useState } from "react";
import TabNavigation from "@/components/layout/TabNavigation";
import TitleSubtitle from "@/components/common/TitleSubtitle";
import UsersContainer from "@/components/users/UserContainer";
import { MOCK_USERS, GROUP_OPTIONS } from "@/utils/constants";

const UsersPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"users" | "groups" | "countries">(
    "users"
  );
  return (
    <div className="min-h-screen   bg-gray-50 ">
      <TitleSubtitle title={"Groups & Users"} subtitle={"All"} align={"left"} />
      <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />

      {/* Main Content */}
      <div>
        <UsersContainer
          users={MOCK_USERS}
          groupOptions={GROUP_OPTIONS}
          type={activeTab}
        />
      </div>
    </div>
  );
};

export default UsersPage;
