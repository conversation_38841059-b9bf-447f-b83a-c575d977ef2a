"use client";
import React, { useState } from "react";

const AddExercise: React.FC = () => {
  const [track, setTrack] = useState("");
  const [problem, setProblem] = useState("");

  const allSelected = track && problem;
  return (
    <div className="flex w-full h-full justify-center  ">
      {/* Form */}
      <div className="w-xl">
        <div className="space-y-8 w-full  pt-2">
          <h2 className="text-xl font-semibold">Add Exercise</h2>

          <div className="relative w-full text-gray-300">
            <label className="absolute -top-2 left-2 bg-white px-1 text-xs text-gray-500 z-10 focus:text-green-500">
              Select a Track
            </label>
            <select
              value={problem}
              onChange={(e) => setTrack(e.target.value)}
              className="w-full border border-gray-300 rounded-lg p-4 hover:border-black text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="" disabled>
                Select a Track
              </option>
              <option>Progress</option>
              <option>Python</option>
              <option>Camp I</option>
            </select>
          </div>

          <select
            id="super-group"
            value={problem}
            className="w-full border border-gray-300 rounded-lg p-4 hover:border-black text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            onChange={(e) => setProblem(e.target.value)}
          >
            <option value="" disabled>
              Select Problem(s)
            </option>

            <option>Bubble Sort</option>
            <option>Merge Sort</option>
            <option>Quick Sort</option>
            <option>Insertion Sort</option>
          </select>
          <div className="flex gap-4 items-center align-middle">
            <select
              value={problem}
              className="w-full border border-gray-300 rounded-lg p-4 hover:border-black text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              <option value="" className="text-gray-200">
                Add to other groups(optional)
              </option>

              <option>Remote</option>
            </select>{" "}
            <label className="flex items-center space-x-2 text-sm leading-tight pb-4 ">
              <input type="checkbox" className="form-checkbox text-wrap" />
              <span>All</span>
            </label>
          </div>
          <button
            className={`w-full rounded-lg p-4 font-semibold ${
              allSelected
                ? "bg-green-600   text-white cursor-pointer"
                : "bg-gray-200 text-gray-500 cursor-not-allowed"
            }`}
            disabled={!allSelected}
          >
            Submit
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddExercise;
