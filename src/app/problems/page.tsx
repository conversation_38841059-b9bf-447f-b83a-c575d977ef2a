"use client";
import { ProblemsControls } from "@/components/ProblemsControls";
import { ProblemsPagination } from "@/components/ProblemsPagination";
import { ProblemsTable } from "@/components/ProblemsTable";
import { useProblems } from "@/hooks/useProblems";
import { useTableControls } from "@/hooks/useTableControls";
import { exportToCSV, printTable } from "@/utils/exportUtils";
import TitleSubtitle from "@/components/common/TitleSubtitle";
import { ProblemsControls } from "@/components/ProblemsControls";
import { ProblemsPagination } from "@/components/ProblemsPagination";
import { ProblemsTable } from "@/components/ProblemsTable";
import { useProblems } from "@/hooks/useProblems";
import { useTableControls } from "@/hooks/useTableControls";
import { exportToCSV, printTable } from "@/utils/exportUtils";
import TitleSubtitle from "@/components/common/TitleSubtitle";

const ProblemsPage = () => {
  const { problems, handleVote } = useProblems();
  const {
    visibleColumns,
    toggleColumn,
    showExportDropdown,
    setShowExportDropdown,
    showFiltersDropdown,
    setShowFiltersDropdown,
    showColumnsDropdown,
    setShowColumnsDropdown,
    filters,
    addFilter,
    removeFilter,
    newFilter,
    setNewFilter,
    filteredAndSortedProblems,
    filteredAndSortedProblems,
  } = useTableControls(problems);

  const handleExportCSV = () => {
    exportToCSV(filteredAndSortedProblems, visibleColumns);
    setShowExportDropdown(false);
  };

  const handlePrint = () => {
    printTable(filteredAndSortedProblems, visibleColumns);
    setShowExportDropdown(false);
  };

  const exportOptions = [
    { label: "Download as CSV", action: handleExportCSV },
    { label: "Print", action: handlePrint },
    { label: "Download as CSV", action: handleExportCSV },
    { label: "Print", action: handlePrint },
  ];

  const closeAllDropdowns = () => {
    setShowExportDropdown(false);
    setShowFiltersDropdown(false);
    setShowColumnsDropdown(false);
  };

  return (
    <>
      <div className="min-h-screen bg-white ">
        <TitleSubtitle
          title={"Problems"}
          subtitle={"All"}
          align={"left"}
          className={"bg-none"}
        />
        <div className="bg-white mb-4">
          <ProblemsControls
            visibleColumns={visibleColumns}
            toggleColumn={toggleColumn}
            showExportDropdown={showExportDropdown}
            setShowExportDropdown={setShowExportDropdown}
            showFiltersDropdown={showFiltersDropdown}
            setShowFiltersDropdown={setShowFiltersDropdown}
            showColumnsDropdown={showColumnsDropdown}
            setShowColumnsDropdown={setShowColumnsDropdown}
            exportOptions={exportOptions}
            filters={filters}
            addFilter={addFilter}
            removeFilter={removeFilter}
            newFilter={newFilter}
            setNewFilter={setNewFilter}
            closeAllDropdowns={closeAllDropdowns}
          />

          <ProblemsTable
            problems={filteredAndSortedProblems}
            visibleColumns={visibleColumns}
            handleVote={handleVote}
          />
          <ProblemsTable
            problems={filteredAndSortedProblems}
            visibleColumns={visibleColumns}
            handleVote={handleVote}
          />

          <ProblemsPagination />
        </div>
          <ProblemsPagination />
        </div>

        {(showExportDropdown || showFiltersDropdown || showColumnsDropdown) && (
          <div className="fixed inset-0 z-40" onClick={closeAllDropdowns} />
        )}
      </div>
    </>
        {(showExportDropdown || showFiltersDropdown || showColumnsDropdown) && (
          <div className="fixed inset-0 z-40" onClick={closeAllDropdowns} />
        )}
      </div>
    </>
  );
};

export default ProblemsPage;

