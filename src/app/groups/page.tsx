import TitleSubtitle from "@/components/common/TitleSubtitle";
import Card from "@/components/groups/Card";
import React from "react";

const Groups = () => {
  return (
    <div>
      <TitleSubtitle
        title="Groups"
        subtitle="All"
        className="mb- ml-4 space-y-2"
      />
      <div className="grid md:grid-cols-3 grid-cols-1 gap-4">
        <Card
          title="AAiT Group 53"
          group="G53"
          members="19"
          timeSpent={187503}
          aveRating={1403}
        />{" "}
        <Card
          title="Group 1"
          group="Group A"
          members="5"
          timeSpent={10}
          aveRating={4.5}
        />
        <Card
          title="Group 1"
          group="Group A"
          members="5"
          timeSpent={10}
          aveRating={4.5}
        />
        <Card
          title="Group 1"
          group="Group A"
          members="5"
          timeSpent={10}
          aveRating={4.5}
        />
        <Card
          title="Group 1"
          group="Group A"
          members="5"
          timeSpent={10}
          aveRating={4.5}
        />
      </div>
    </div>
  );
};

export default Groups;
