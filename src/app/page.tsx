"use client";
import LandingLevelUpSection from "@/components/common/LandingLevelUpSection";

// import Completion from "@/components/head-home/completion";
// import DailySolve from "@/components/head-home/dailySolve";
// import ProblemSolvingChart from "@/components/head-home/ProblemSolvingChart";
// import Summary from "@/components/head-home/summary";
// import SubmissionTable from "@/components/SubmissionTable";
// import { Submission } from "@/types/submission";

// const submissions: Submission[] = [
//   {
//     id: 1,
//     name: "<PERSON>",
//     avatarUrl: "/avatar-fiona.jpg",
//     problem: "Implement Trie (Prefix Tree)",
//     timeSpent: 45,
//     language: "Python",
//     added: "2mo",
//   },
//   {
//     id: 2,
//     name: "<PERSON>",
//     avatarUrl: "/avatar-fiona.jpg",
//     problem: "Subarray Sum Equals K",
//     timeSpent: 45,
//     language: "Python",
//     added: "2mo",
//   },
//   {
//     id: 3,
//     name: "<PERSON>",
//     avatarUrl: "/avatar-fiona.jpg",
//     problem: "Rabbits in Forest",
//     timeSpent: 45,
//     language: "Python",
//     added: "2mo",
//   },
//   {
//     id: 4,
//     name: "Fiona Murugi",
//     avatarUrl: "/avatar-fiona.jpg",
//     problem: "Count the Number of Comple",
//     timeSpent: 45,
//     language: "Python",
//     added: "2mo",
//   },
//   {
//     id: 5,
//     name: "Nathan Githinji Rugo",
//     problem: "D - Swap Letters",
//     timeSpent: 60,
//     language: "Python",
//     added: "3mo",
//   },
//   {
//     id: 6,
//     name: "Nathan Githinji Rugo",
//     problem: "F - Good SubRectangle",
//     timeSpent: 75,
//     language: "Python",
//     added: "3mo",
//   },
// ];import LatestProblems from "@/components/LatestProblemsHomePage/LatestProblems"

export default function Home() {
    return (
        <div className="container mx-auto px-4 space-y-4 ">
            <LandingLevelUpSection />
            {/* <div className=" md:flex  md:space-y-0 mb-4  md:gap-4 ">
        <DailySolve />
        <ProblemSolvingChart />
      </div>
      <Summary />
      <SubmissionTable submissions={submissions} />
      <Completion />
      <LatestProblems/>
      <LatestProblems/> */}
        </div>
    );
}
