"use client";
import React, { useState } from "react";

const AddContest: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"standard" | "super">("standard");
  const [selectedContestToDelete, setSelectedContestToDelete] = useState("");

  return (
    <div className="flex flex-col justify-center items-center min-h-fit w-full ">
      {/* Tabs */}
      <div className="flex space-x-6 justify-center w-full max-w-xl text-sm pt-4">
        <button
          className={`pb-2 w-1/2 ${
            activeTab === "standard"
              ? "border-b-2 border-green-600 text-black font-bold"
              : "text-gray-500"
          }`}
          onClick={() => setActiveTab("standard")}
        >
          Standard Contest
        </button>
        <button
          className={`pb-2 w-1/2 ${
            activeTab === "super"
              ? "border-b-2 border-green-600 text-black"
              : "text-gray-400"
          }`}
          onClick={() => setActiveTab("super")}
        >
          Super Contest
        </button>
      </div>

      {/* Form */}
      <div className="mt-6 flex justify-center items-center w-full">
        {activeTab === "standard" ? (
          <div className="space-y-4 w-full max-w-xl ">
            <h2 className="text-xl font-semibold">Add Contest</h2>
            <label className="flex items-center space-x-2 text-sm leading-tight pb-4">
              <input type="checkbox" className="form-checkbox text-wrap" />
              <span>
                Unrated contest (This will add virtual participants but the
                contest will be <br />
                unrated.)
              </span>
            </label>
            <div className="relative w-full ">
              <label
                className="absolute -top-2 left-2 bg-white px-1 text-xs text-gray-500 z-10"
                htmlFor="super-group"
              >
                Select Super Group
              </label>
              <select
                id="super-group"
                className="w-full border border-gray-300 rounded p-2 pt-5 text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option>Remote</option>
              </select>
            </div>
            <input
              type="text"
              placeholder="Paste contest link"
              className="w-full border rounded p-2 py-3  hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            />
            <button
              className="w-full bg-gray-200 text-gray-500 rounded p-2 cursor-not-allowed font-semibold"
              disabled
            >
              Submit
            </button>
          </div>
        ) : (
          <div className="space-y-4 w-full max-w-xl ">
            <h2 className="text-lg font-semibold">Add Super Contest</h2>
            <input
              type="text"
              placeholder="Contest Name"
              className="w-full border rounded p-2 py-3 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            />
            <input
              type="text"
              placeholder="Paste contest link for Div1"
              className="w-full border rounded p-2 py-3 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            />
            <input
              type="text"
              placeholder="Paste contest link for Div2"
              className="w-full border rounded p-2 py-3 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            />
            <input
              type="text"
              placeholder="Paste contest link for Div3"
              className="w-full border rounded p-2 py-3 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            />
            <div className="relative w-full">
              <label
                className="absolute -top-2 left-2 bg-white px-1 text-xs text-gray-500 z-10"
                htmlFor="super-group"
              >
                Select Super Group
              </label>
              <select
                id="super-group"
                className="w-full border border-gray-300 rounded p-2 pt-5 text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <option>Remote</option>
              </select>
            </div>
            <button
              className="w-full bg-gray-200 text-gray-400 rounded p-2 cursor-not-allowed font-semibold"
              disabled
            >
              Preprocess
            </button>
            <button
              className="w-full bg-gray-200 text-gray-400 rounded p-2 cursor-not-allowed font-semibold"
              disabled
            >
              Submit
            </button>
          </div>
        )}
      </div>

      {/* Delete section */}
      <div className="mt-10 pt-6 w-full max-w-xl mb-5">
        <h2 className="text-xl font-semibold mb-2">Delete contest</h2>
        <div className="flex items-center space-x-4 py-2 ">
          <select
            className="flex-1 border rounded py-3 p-2 text-gray-500 mr-1 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500"
            value={selectedContestToDelete}
            onChange={(e) => setSelectedContestToDelete(e.target.value)}
          >
            <option>Select contest</option>
          </select>
          <button
            className="bg-gray-200 text-gray-400 px-4 py-2 rounded cursor-not-allowed font-semibold"
            disabled
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddContest;
