"use client";
import AddSessionForm from "@/components/add-session/AddSessionForm";
import React, { useState } from "react";

const AddSession: React.FC = () => {
  const [activeTab, setActiveTab] = useState<"create" | "update">("create");
  const [selectedContestToDelete, setSelectedContestToDelete] = useState("");

  const [sessionToUpdate, setSessionToUpdate] = useState("");

  return (
    <div className="flex flex-col justify-center items-center min-h-fit w-full">
      <div className="w-full max-w-xl mx-auto text-start bg-white">
        <h2 className="text-xl font-semibold w-full mb-4">Add Session</h2>
        {/* Tabs */}
        <div className="flex space-x-6 justify-center w-full max-w-xl text-sm pt-4">
          <button
            className={`pb-2 w-1/2 ${
              activeTab === "create"
                ? "border-b-2 border-green-600 text-black font-bold"
                : "text-gray-500"
            }`}
            onClick={() => setActiveTab("create")}
          >
            Create
          </button>
          <button
            className={`pb-2 w-1/2 ${
              activeTab === "update"
                ? "border-b-2 border-green-600 text-black"
                : "text-gray-400"
            }`}
            onClick={() => setActiveTab("update")}
          >
            Update
          </button>
        </div>

        {/* Form */}
        <div className="mt-6 flex justify-center items-center w-full">
          {activeTab === "create" ? (
            <div className="space-y-4 w-full max-w-xl ">
              <label className="flex items-center space-x-2 text-sm  pb-4">
                <span>
                  Your group has a Google account attached. You can now
                  automatically send calendar invites from{" "}
                  <span className="font-bold">
                    {" "}
                    <EMAIL>.
                  </span>{" "}
                  <span className="underline text-blue-800">Change</span>
                </span>
              </label>
              <AddSessionForm />
            </div>
          ) : (
            <div className="space-y-4 w-full max-w-xl ">
              <label className="flex items-center space-x-2 text-sm  pb-4">
                <span>
                  Your group has a Google account attached. You can now
                  automatically send calendar invites from
                  <span className="font-bold">
                    {" "}
                    <EMAIL>
                  </span>
                  . <span className="underline text-blue-800">Change</span>
                </span>
              </label>
              <div className="relative w-full text-gray-300">
                <select
                  value={sessionToUpdate}
                  onChange={(e) => setSessionToUpdate(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-4 hover:border-black text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="" className="text-gray-200">
                    Select session to update
                  </option>
                  <option>Progress</option>
                  <option>Python</option>
                  <option>Camp I</option>
                  <option>Remote</option>
                </select>
              </div>
              {sessionToUpdate && <AddSessionForm />}
              {!sessionToUpdate && (
                <button
                  className="w-full  rounded-md p-3 font-semibold bg-gray-200 text-gray-400 cursor-pointer"
                  disabled
                >
                  Submit
                </button>
              )}
            </div>
          )}
        </div>

        {/* Delete section */}
        <div className="mt-10 pt-6 w-full max-w-xl mb-5">
          <h2 className="text-xl font-semibold mb-2">Delete session</h2>
          <div className="flex items-center space-x-4 py-2 ">
            <select
              className="flex-1 border  py-4  text-gray-500 mr-1 hover:border-black border-gray-300 focus:outline-none focus:ring-2 focus:ring-green-500 rounded-md p-3"
              value={selectedContestToDelete}
              onChange={(e) => setSelectedContestToDelete(e.target.value)}
            >
              <option>Select session</option>
            </select>
            <button
              className="bg-gray-200 text-gray-400 px-4 py-3 rounded cursor-not-allowed font-semibold"
              disabled
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddSession;
