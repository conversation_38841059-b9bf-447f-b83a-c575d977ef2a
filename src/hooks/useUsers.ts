// src/hooks/useUsers.ts

import { useEffect } from "react";
import { useAppSelector } from "./useAppSelector";
import { useAppDispatch } from "./useAppDispatch";
import { User } from "@/types";
import {
  setSearchTerm,
  setSelectedGroup,
  setViewMode,
  setUsers,
} from "../store/slices/usersSlice";

export const useUsers = (initialUsers?: User[]) => {
  const dispatch = useAppDispatch();
  const {
    users,
    filteredUsers,
    loading,
    error,
    searchTerm,
    selectedGroup,
    viewMode,
  } = useAppSelector((state) => state.users);

  useEffect(() => {
    if (initialUsers) {
      dispatch(setUsers(initialUsers));
    }
  }, [dispatch, initialUsers]);

  const handleSearchChange = (term: string) => {
    dispatch(setSearchTerm(term));
  };

  const handleGroupChange = (group: string) => {
    dispatch(setSelectedGroup(group));
  };

  const handleViewModeChange = (mode: "card" | "list") => {
    dispatch(setViewMode(mode));
  };

  return {
    users,
    filteredUsers,
    loading,
    error,
    searchTerm,
    selectedGroup,
    viewMode,
    handleSearchChange,
    handleGroupChange,
    handleViewModeChange,
  };
};
