import { useState } from 'react';

interface Problem {
  id: number;
  name: string;
  difficulty: string;
  tags: string[];
  solved: boolean;
  addedDays: number;
  votes: number;
  userVote: number;
  link: string;
}

export const useProblems = () => {
  const [problems, setProblems] = useState<Problem[]>([
    {
      id: 1,
      name: 'Binary Subarrays With Sum',
      difficulty: 'Medium',
      tags: ['Array', 'Hash Table'],
      solved: false,
      addedDays: 1,
      votes: 1,
      userVote: 1,
      link: '#'
    },
    {
      id: 2,
      name: 'Recover Binary Search Tree',
      difficulty: 'Medium',
      tags: ['Tree', 'Depth-First Search'],
      solved: false,
      addedDays: 3,
      votes: -1,
      userVote: -1,
      link: '#'
    },
    {
      id: 3,
      name: 'Unique Paths III',
      difficulty: 'Hard',
      tags: ['Array', 'Backtracking'],
      solved: false,
      addedDays: 3,
      votes: 0,
      userVote: 0,
      link: '#'
    },
    {
      id: 4,
      name: 'Find the Substring With Maximum Cost',
      difficulty: 'Medium',
      tags: ['Array', 'Hash Table'],
      solved: false,
      addedDays: 3,
      votes: 0,
      userVote: 0,
      link: '#'
    },
    {
      id: 5,
      name: 'Maximum Subarray',
      difficulty: 'Medium',
      tags: ['Array', 'Divide and Conquer'],
      solved: false,
      addedDays: 3,
      votes: 0,
      userVote: 0,
      link: '#'
    },
    {
      id: 6,
      name: 'Check If Digits Are Equal in String After Operations II',
      difficulty: 'Hard',
      tags: ['Math', 'String'],
      solved: false,
      addedDays: 4,
      votes: 0,
      userVote: 0,
      link: '#'
    },
    {
      id: 7,
      name: 'G - Equivalent Strings',
      difficulty: 'Hard',
      tags: ['Contest'],
      solved: false,
      addedDays: 6,
      votes: 0,
      userVote: 0,
      link: '#'
    }
  ]);

  const handleVote = (problemId: number, voteType: number) => {
    setProblems(prevProblems => 
      prevProblems.map(problem => {
        if (problem.id === problemId) {
          let newVotes = problem.votes;
          let newUserVote = problem.userVote;
          
          if (problem.userVote !== 0) {
            newVotes -= problem.userVote;
          }
          
          if (problem.userVote === voteType) {
    
            newUserVote = 0;
          } else {
            newUserVote = voteType;
            newVotes += voteType;
          }
          
          return {
            ...problem,
            votes: newVotes,
            userVote: newUserVote
          };
        }
        return problem;
      })
    );
  };

  return { problems, handleVote };
};