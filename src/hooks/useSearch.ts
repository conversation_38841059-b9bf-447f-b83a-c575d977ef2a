// src/hooks/useSearch.ts

import { useMemo } from "react";
import { User } from "@/types/index";

export const useSearch = (users: User[], searchTerm: string) => {
  return useMemo(() => {
    if (!searchTerm) return users;

    return users.filter(
      (user) =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.group.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [users, searchTerm]);
};
