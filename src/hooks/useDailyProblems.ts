import { useAppSelector } from "./useAppSelector";
import { useAppDispatch } from "./useAppDispatch";
import { useEffect, useState, useRef } from "react";
import { Problem } from "@/types/dailyProblemsTypes";

import {
  setSearchTerm,
  setSelectedSuperGroup,
  setSelectedProblems,
  setError,
  setAllProblems,
  setFilteredProblems,
} from "../store/slices/dailyProblemsSlice";

export const useDailyProblems = (
  initialProblems?: Problem[],
  allProblemsInput?: Problem[]
) => {
  const [isActive, setIsActive] = useState(true);
  const dispatch = useAppDispatch();
  const prevAllProblemsInput = useRef<Problem[]>(initialProblems);

  const {
    dailyProblems,
    filteredProblems,
    loading,
    error,
    searchTerm,
    selectedSuperGroup,
    selectedProblems,
  } = useAppSelector((state) => state.dailyProblems);

  // Set initial selected problems
  useEffect(() => {
    if (initialProblems) {
      dispatch(setSelectedProblems(initialProblems));
    }
  }, [dispatch, initialProblems]);

  // Set all problems from input (e.g., from component prop)
  useEffect(() => {
    // Only update if allProblemsInput has actually changed
    if (
      allProblemsInput && 
      allProblemsInput.length > 0 &&
      allProblemsInput !== prevAllProblemsInput.current
    ) {
      // Deep comparison to ensure actual content change
      const hasChanged = !prevAllProblemsInput.current ||
        prevAllProblemsInput.current.length !== allProblemsInput.length ||
        prevAllProblemsInput.current.some((p, i) => p.id !== allProblemsInput[i]?.id);
      
      if (hasChanged) {
        dispatch(setAllProblems(allProblemsInput));
        dispatch(setFilteredProblems(allProblemsInput));
        prevAllProblemsInput.current = allProblemsInput;
      }
    }
  }, [dispatch, allProblemsInput]);

  const handleSuperGroupChange = (value: string) => {
    dispatch(setSelectedSuperGroup(value));
    if (value === "") {
      dispatch(setError("Please select a super group."));
    } else {
      dispatch(setError(""));
    }
  };

  const handleProblemsChange = (problems: Problem[]) => {
    dispatch(setSelectedProblems(problems));
    // if (problems.length === 0) {
    //   dispatch(setError("Please select at least one problem."));
    // } else {
    //   dispatch(setError(""));
    // }
  };

  const handleAddProblemsSubmit = () => {
    if (selectedProblems.length === 0) {
      dispatch(setError("Please select at least one problem."));
      return;
    }
    // Handle the submission logic here
    console.log("Selected Problems:", selectedProblems);
    // Reset the selected problems after submission
    dispatch(setSelectedProblems([]));
    setIsActive(!isActive);
    dispatch(setError(""));
  };

  const handleSelect = (problem: Problem): void => {
    const alreadySelected = selectedProblems.some((p) => p.id === problem.id);
    let newValue: Problem[];
    if (alreadySelected) {
      newValue = selectedProblems.filter((p) => p.id !== problem.id);
    } else {
      newValue = [...selectedProblems, problem];
    }
    dispatch(setSelectedProblems(newValue));
  };

  const handleRemoveTag = (problemId: string): void => {
    const newValue = selectedProblems.filter((p) => p.id !== problemId);
    dispatch(setSelectedProblems(newValue));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const searchValue: string = e.target.value;
    dispatch(setSearchTerm(searchValue));
  };

  const isSelected = (problemId: string): boolean => {
    return selectedProblems.some((p: Problem) => p.id === problemId);
  };

  const handleSaveOrderSubmit = async () => {};

  return {
    dailyProblems,
    filteredProblems,
    loading,
    error,
    searchTerm,
    selectedSuperGroup,
    isActive,
    handleSuperGroupChange,
    handleProblemsChange,
    handleSelect,
    handleRemoveTag,
    handleAddProblemsSubmit,
    handleSaveOrderSubmit,
    handleSearchChange,
    isSelected,
    selectedProblems,
  };
};