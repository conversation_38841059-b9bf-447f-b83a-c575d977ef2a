import { useAppSelector } from "./useAppSelector";
import { useAppDispatch } from "./useAppDispatch";
import {useCallback} from 'react'
import {  setCurrentSession,
  initializeSessionUsers,
  updateUserAttendanceStatus,
  setAllUsersPresent,
  setSessionNumber,
  setSessionType,
  clearCurrentSession,
  setValidationErrors,
  clearError,
  resetValidationErrors,
  submitAttendance,
  fetchAvailableSessions, } from '@/store/slices/attendanceSlice';
import { AttendanceStatus, User,  SessionType } from '@/types/index';

export const useAttendance = () => {
  const dispatch = useAppDispatch();

  //SELECTORS
  const {  currentSession,
    sessionUsers,
    attendanceSessions,
    availableSessions,
    isLoading,
    isSubmitting,
    error,
    validationErrors,} = useAppSelector((state) => state.attendance);

  // Action dispatchers
  const initializeSession = useCallback((
    sessionNumber: number,
    sessionType: SessionType,
    group: string,
    users: User[]
  ) => {
    dispatch(setCurrentSession({ sessionNumber, sessionType, group }));
    dispatch(initializeSessionUsers(users));
  }, [dispatch]);

  const updateUserStatus = useCallback((userId: string, status: AttendanceStatus) => {
    dispatch(updateUserAttendanceStatus({ userId, status }));
  }, [dispatch]);

  const markAllPresent = useCallback(() => {
    dispatch(setAllUsersPresent());
  }, [dispatch]);

  const updateSessionNumber = useCallback((sessionNumber: number) => {
    dispatch(setSessionNumber(sessionNumber));
  }, [dispatch]);

  const updateSessionType = useCallback((sessionType: SessionType) => {
    dispatch(setSessionType(sessionType));
  }, [dispatch]);

  const clearSession = useCallback(() => {
    dispatch(clearCurrentSession());
  }, [dispatch]);

  const clearErrors = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const resetErrors = useCallback(() => {
    dispatch(resetValidationErrors());
  }, [dispatch]);

  // Validation function
  const validateAttendanceForm = useCallback(() => {
    const errors: { session?: string | null; sessionType?: string | null } = {};
    
    if (!currentSession.sessionNumber) {
      errors.session = "session must be a 'number' type, but the final value was: NaN (cast from the value \"\").";
    }
    
    if (!currentSession.sessionType) {
      errors.sessionType = "type must be a 'number' type, but the final value was: NaN (cast from the value \"\").";
    }

    if (errors.session || errors.sessionType) {
      dispatch(setValidationErrors(errors));
      return false;
    }

    return true;
  }, [currentSession, dispatch]);

  // Submit attendance
  const handleSubmitAttendance = useCallback(async () => {
    if (!validateAttendanceForm()) {
      return false;
    }

    if (!currentSession.sessionNumber || !currentSession.sessionType || !currentSession.group) {
      return false;
    }

    const attendanceRecords = Object.values(sessionUsers).map(({ user, status }) => ({
      userId: user.id,
      status,
    }));

    try {
      await dispatch(submitAttendance({
        sessionNumber: currentSession.sessionNumber,
        sessionType: currentSession.sessionType,
        group: currentSession.group,
        attendanceRecords,
      })).unwrap();
      
      return true;
    } catch (error) {
      console.error('Failed to submit attendance:', error);
      return false;
    }
  }, [currentSession, sessionUsers, validateAttendanceForm, dispatch]);

  // Fetch available sessions
  const loadAvailableSessions = useCallback(() => {
    dispatch(fetchAvailableSessions());
  }, [dispatch]);

  // Computed values
  const sessionUsersList = Object.values(sessionUsers);
  const isSessionConfigured = !!(currentSession.sessionNumber && currentSession.sessionType);
  const hasValidationErrors = !!(validationErrors.session || validationErrors.sessionType);

  // Get attendance statistics
  const getAttendanceStats = useCallback(() => {
    const stats = {
      present: 0,
      late: 0,
      absent: 0,
      excused: 0,
      total: sessionUsersList.length,
    };

    sessionUsersList.forEach(({ status }) => {
      switch (status) {
        case 'PRESENT':
          stats.present++;
          break;
        case 'LATE':
          stats.late++;
          break;
        case 'ABSENT':
          stats.absent++;
          break;
        case 'EXCUSED':
          stats.excused++;
          break;
      }
    });

    return stats;
  }, [sessionUsersList]);

  return {
    // State
    currentSession,
    sessionUsers: sessionUsersList,
    attendanceSessions,
    availableSessions,
    isLoading,
    isSubmitting,
    error,
    validationErrors,
    
    // Computed values
    isSessionConfigured,
    hasValidationErrors,
    
    // Actions
    initializeSession,
    updateUserStatus,
    markAllPresent,
    updateSessionNumber,
    updateSessionType,
    clearSession,
    clearErrors,
    resetErrors,
    handleSubmitAttendance,
    loadAvailableSessions,
    getAttendanceStats,
  };
};
// export default useAttendance;