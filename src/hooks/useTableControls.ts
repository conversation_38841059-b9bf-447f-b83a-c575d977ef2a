import { useMemo, useState } from 'react';

interface Problem {
  id: number;
  name: string;
  difficulty: string;
  tags: string[];
  solved: boolean;
  addedDays: number;
  votes: number;
  userVote: number;
  link: string;
}

interface Filter {
  id: number;
  column: string;
  operator: string;
  value: string;
}

export const useTableControls = (problems: Problem[]) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('All');
  const [selectedTag, setSelectedTag] = useState('All');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  
  const [showExportDropdown, setShowExportDropdown] = useState(false);
  const [showFiltersDropdown, setShowFiltersDropdown] = useState(false);
  const [showColumnsDropdown, setShowColumnsDropdown] = useState(false);
  
  const [visibleColumns, setVisibleColumns] = useState({
    id: false,
    difficulty: true,
    name: true,
    tag: true,
    track: false,
    contest: false,
    solved: true,
    added: true,
    vote: true,
    link: true
  });

  const [filters, setFilters] = useState<Filter[]>([]);
  const [newFilter, setNewFilter] = useState({
    column: 'Id',
    operator: 'contains',
    value: ''
  });

  const addFilter = () => {
    if (newFilter.value.trim()) {
      setFilters([...filters, { ...newFilter, id: Date.now() }]);
      setNewFilter({ column: 'Id', operator: 'contains', value: '' });
    }
  };

  const removeFilter = (filterId: number) => {
    setFilters(filters.filter(f => f.id !== filterId));
  };

  const toggleColumn = (columnKey: string) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: !prev[columnKey]
    }));
  };

  const filteredAndSortedProblems = useMemo(() => {
    let filtered = problems.filter(problem => {
      const matchesSearch = problem.name.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });

    return filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      switch (sortBy) {
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'difficulty':
          const difficultyOrder = { 'Easy': 1, 'Medium': 2, 'Hard': 3 };
          aValue = difficultyOrder[a.difficulty as keyof typeof difficultyOrder];
          bValue = difficultyOrder[b.difficulty as keyof typeof difficultyOrder];
          break;
        case 'added':
          aValue = a.addedDays;
          bValue = b.addedDays;
          break;
        default:
          return 0;
      }
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
  }, [problems, searchTerm, sortBy, sortOrder]);

  return {
    searchTerm,
    setSearchTerm,
    selectedDifficulty,
    setSelectedDifficulty,
    selectedTag,
    setSelectedTag,
    sortBy,
    setSortBy,
    sortOrder,
    setSortOrder,
    visibleColumns,
    toggleColumn,
    showExportDropdown,
    setShowExportDropdown,
    showFiltersDropdown,
    setShowFiltersDropdown,
    showColumnsDropdown,
    setShowColumnsDropdown,
    filters,
    addFilter,
    removeFilter,
    newFilter,
    setNewFilter,
    filteredAndSortedProblems
  };
};