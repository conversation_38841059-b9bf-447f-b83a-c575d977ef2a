<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="MuiBox-root css-c2uxn6 iconify iconify--fluent-emoji" sx="[object Object]" width="1em" height="1em" preserveAspectRatio="xMidYMid meet" viewBox="0 0 32 32"><g fill="none"><g filter="url(#iconify-react-2268)"><path fill="url(#iconify-react-2264)" d="M15.821 11.106c-.17.662-.749 1.156-1.433 1.498c-1.939.67-3.47 1.316-4.738 1.942c-1.122.555-1.11 2.308.015 2.855c1.315.639 2.966 1.252 4.8 1.98c.493.195 1.19.747 1.363 1.426a56 56 0 0 0 2.063 6.35c.621 1.587 2.158 1.585 2.783 0c.81-2.059 1.427-4.282 2.122-6.397a2.02 2.02 0 0 1 1.294-1.395a70 70 0 0 0 4.721-1.889c1.26-.555 1.286-2.356.042-2.946a56 56 0 0 0-4.771-2.009a2.16 2.16 0 0 1-1.339-1.398c-.754-2.31-1.232-4.27-2.034-6.304c-.625-1.586-2.264-1.537-2.89.048c-.798 2.028-1.355 4.045-1.998 6.239"></path></g><path fill="url(#iconify-react-2265)" d="M15.821 11.106c-.17.662-.749 1.156-1.433 1.498c-1.939.67-3.47 1.316-4.738 1.942c-1.122.555-1.11 2.308.015 2.855c1.315.639 2.966 1.252 4.8 1.98c.493.195 1.19.747 1.363 1.426a56 56 0 0 0 2.063 6.35c.621 1.587 2.158 1.585 2.783 0c.81-2.059 1.427-4.282 2.122-6.397a2.02 2.02 0 0 1 1.294-1.395a70 70 0 0 0 4.721-1.889c1.26-.555 1.286-2.356.042-2.946a56 56 0 0 0-4.771-2.009a2.16 2.16 0 0 1-1.339-1.398c-.754-2.31-1.232-4.27-2.034-6.304c-.625-1.586-2.264-1.537-2.89.048c-.798 2.028-1.355 4.045-1.998 6.239"></path><g filter="url(#iconify-react-2269)"><path fill="url(#iconify-react-2266)" d="M7.768 5.816c-.055.213-.241.372-.461.482c-.624.216-1.117.424-1.525.626c-.361.178-.358.742.005.918c.423.206.954.403 1.544.637c.16.063.383.241.44.46c.171.673.405 1.385.663 2.043c.2.511.695.51.896 0c.26-.662.46-1.378.683-2.059a.65.65 0 0 1 .417-.449a22 22 0 0 0 1.52-.607a.523.523 0 0 0 .013-.949c-.428-.202-.96-.437-1.536-.646a.7.7 0 0 1-.431-.45c-.243-.744-.397-1.374-.655-2.03c-.2-.51-.729-.494-.93.016c-.257.653-.436 1.302-.643 2.008"></path></g><path fill="url(#iconify-react-2267)" d="M7.768 5.816c-.055.213-.241.372-.461.482c-.624.216-1.117.424-1.525.626c-.361.178-.358.742.005.918c.423.206.954.403 1.544.637c.16.063.383.241.44.46c.171.673.405 1.385.663 2.043c.2.511.695.51.896 0c.26-.662.46-1.378.683-2.059a.65.65 0 0 1 .417-.449a22 22 0 0 0 1.52-.607a.523.523 0 0 0 .013-.949c-.428-.202-.96-.437-1.536-.646a.7.7 0 0 1-.431-.45c-.243-.744-.397-1.374-.655-2.03c-.2-.51-.729-.494-.93.016c-.257.653-.436 1.302-.643 2.008"></path><g filter="url(#iconify-react-2270)"><path fill="url(#iconify-react-2271)" d="M5.226 20.066c-.076.297-.336.518-.643.672c-.87.3-1.558.59-2.127.872c-.503.249-.498 1.035.007 1.28c.59.288 1.331.563 2.154.89c.222.087.534.335.612.64c.24.938.566 1.932.926 2.85c.279.712.969.711 1.249 0c.364-.924.64-1.922.952-2.872a.9.9 0 0 1 .581-.626a31 31 0 0 0 2.12-.847a.73.73 0 0 0 .018-1.323a25 25 0 0 0-2.141-.901a.97.97 0 0 1-.601-.628c-.339-1.037-.553-1.916-.913-2.83c-.28-.71-1.017-.689-1.297.023c-.358.91-.609 1.815-.897 2.8"></path></g><defs><linearGradient id="iconify-react-2264" x1="26.129" x2="15.052" y1="11.271" y2="22.931" gradientUnits="userSpaceOnUse"><stop stop-color="#FFCF5A"></stop><stop offset="1" stop-color="#FEA254"></stop></linearGradient><linearGradient id="iconify-react-2265" x1="14.754" x2="20.792" y1="29.29" y2="16.554" gradientUnits="userSpaceOnUse"><stop stop-color="#FF886D"></stop><stop offset="1" stop-color="#FF886D" stop-opacity="0"></stop></linearGradient><linearGradient id="iconify-react-2266" x1="11.086" x2="7.521" y1="5.869" y2="9.622" gradientUnits="userSpaceOnUse"><stop stop-color="#FFDA72"></stop><stop offset="1" stop-color="#F7A967"></stop></linearGradient><linearGradient id="iconify-react-2267" x1="8.886" x2="8.886" y1="11.98" y2="7.855" gradientUnits="userSpaceOnUse"><stop stop-color="#FDA071"></stop><stop offset="1" stop-color="#FDA071" stop-opacity="0"></stop></linearGradient><filter id="iconify-react-2268" width="21.607" height="25.193" x="8.415" y="3.404" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix><feOffset dx=".25" dy="-.25"></feOffset><feGaussianBlur stdDeviation=".25"></feGaussianBlur><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite><feColorMatrix values="0 0 0 0 0.937255 0 0 0 0 0.482353 0 0 0 0 0.329412 0 0 0 1 0"></feColorMatrix><feBlend in2="shape" result="effect1_innerShadow_18_454"></feBlend><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix><feOffset dx=".25" dy=".25"></feOffset><feGaussianBlur stdDeviation=".5"></feGaussianBlur><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite><feColorMatrix values="0 0 0 0 0.737255 0 0 0 0 0.615686 0 0 0 0 0.415686 0 0 0 1 0"></feColorMatrix><feBlend in2="effect1_innerShadow_18_454" result="effect2_innerShadow_18_454"></feBlend><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix><feOffset dx="-.4" dy=".25"></feOffset><feGaussianBlur stdDeviation=".2"></feGaussianBlur><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite><feColorMatrix values="0 0 0 0 1 0 0 0 0 0.901961 0 0 0 0 0.458824 0 0 0 1 0"></feColorMatrix><feBlend in2="effect2_innerShadow_18_454" result="effect3_innerShadow_18_454"></feBlend></filter><filter id="iconify-react-2269" width="6.945" height="8.048" x="5.413" y="3.418" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix><feOffset dx=".1" dy=".1"></feOffset><feGaussianBlur stdDeviation=".1"></feGaussianBlur><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite><feColorMatrix values="0 0 0 0 0.803922 0 0 0 0 0.6 0 0 0 0 0.262745 0 0 0 1 0"></feColorMatrix><feBlend in2="shape" result="effect1_innerShadow_18_454"></feBlend><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix><feOffset dx="-.1" dy=".1"></feOffset><feGaussianBlur stdDeviation=".1"></feGaussianBlur><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite><feColorMatrix values="0 0 0 0 1 0 0 0 0 0.882353 0 0 0 0 0.423529 0 0 0 1 0"></feColorMatrix><feBlend in2="effect1_innerShadow_18_454" result="effect2_innerShadow_18_454"></feBlend></filter><filter id="iconify-react-2270" width="9.556" height="11.233" x="2.081" y="16.571" color-interpolation-filters="sRGB" filterUnits="userSpaceOnUse"><feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood><feBlend in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feBlend><feColorMatrix in="SourceAlpha" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"></feColorMatrix><feOffset dx=".15" dy="-.15"></feOffset><feGaussianBlur stdDeviation=".15"></feGaussianBlur><feComposite in2="hardAlpha" k2="-1" k3="1" operator="arithmetic"></feComposite><feColorMatrix values="0 0 0 0 1 0 0 0 0 0.458824 0 0 0 0 0.462745 0 0 0 1 0"></feColorMatrix><feBlend in2="shape" result="effect1_innerShadow_18_454"></feBlend></filter><radialGradient id="iconify-react-2271" cx="0" cy="0" r="1" gradientTransform="matrix(-4.2802 5.63466 -5.4871 -4.1681 10.014 18.567)" gradientUnits="userSpaceOnUse"><stop offset=".365" stop-color="#FFBC64"></stop><stop offset="1" stop-color="#FF8F6B"></stop></radialGradient></defs></g></svg>