<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectTasksOptions">
    <TaskOptions isEnabled="false">
      <option name="arguments" value="$FileName$:$FileNameWithoutExtension$.css" />
      <option name="checkSyntaxErrors" value="true" />
      <option name="description" />
      <option name="exitCodeBehavior" value="ERROR" />
      <option name="fileExtension" value="scss" />
      <option name="immediateSync" value="true" />
      <option name="name" value="SCSS" />
      <option name="output" value="$FileNameWithoutExtension$.css:$FileNameWithoutExtension$.css.map" />
      <option name="outputFilters">
        <array />
      </option>
      <option name="outputFromStdout" value="false" />
      <option name="program" value="sass" />
      <option name="runOnExternalChanges" value="true" />
      <option name="scopeName" value="Project Files" />
      <option name="trackOnlyRoot" value="true" />
      <option name="workingDir" value="$FileDir$" />
      <envs />
    </TaskOptions>
  </component>
</project>